# SteamPY - Steam游戏交易平台

一个完整的、现代化的Steam游戏交易平台，使用纯HTML、CSS和JavaScript构建，无需安装任何依赖。

## 🌟 特性

### ✅ 完整功能
- **首页** - 精美的着陆页面，包含英雄区域、特色产品、分类展示
- **产品商城** - 完整的产品浏览、搜索、筛选功能
- **Steam钱包充值** - 多种充值选项、支付方式、优惠券系统
- **用户认证** - 登录/注册页面，支持第三方登录
- **订单管理** - 完整的订单追踪、状态管理、配送信息
- **个人中心** - 用户资料、安全设置、积分系统、交易记录
- **客服支持** - 多渠道客服、工单系统、FAQ问答

### 🎨 设计特色
- **Steam官方主题** - 使用Steam品牌色彩和设计语言
- **响应式设计** - 完美适配桌面和移动设备
- **现代UI组件** - 卡片、按钮、表单、导航等精美组件
- **流畅动画** - 悬停效果、过渡动画、交互反馈
- **商业化元素** - 折扣标签、信任指标、社会证明

### 💻 技术特点
- **纯HTML/CSS/JS** - 无需Node.js、npm或任何构建工具
- **即开即用** - 双击HTML文件即可在浏览器中运行
- **零依赖** - 除了Font Awesome图标，无外部依赖
- **轻量级** - 快速加载，性能优异
- **跨浏览器兼容** - 支持所有现代浏览器

## 📁 文件结构

```
web/
├── index.html          # 首页 - 主页面入口
├── products.html       # 产品商城 - 商品浏览购买
├── wallet.html         # Steam钱包 - 充值服务
├── login.html          # 用户登录 - 身份验证
├── register.html       # 用户注册 - 账户创建
├── orders.html         # 订单管理 - 订单追踪
├── profile.html        # 个人中心 - 用户资料管理
├── support.html        # 客服支持 - 帮助和支持
├── styles/
│   └── main.css        # 主样式文件 (1500+ 行)
├── scripts/
│   ├── main.js         # 核心功能脚本
│   ├── products.js     # 产品页面功能
│   └── wallet.js       # 钱包页面功能
└── README.md           # 项目说明文档
```

## 🚀 快速开始

### 方法一：直接运行
1. 下载项目文件到本地
2. 进入 `web` 目录
3. 双击 `index.html` 文件
4. 网站将在默认浏览器中打开

### 方法二：本地服务器
```bash
cd web
python -m http.server 8080
# 或者使用其他静态服务器
```
然后访问 `http://localhost:8080`

## 📱 页面介绍

### 🏠 首页 (index.html)
- **英雄区域**: 突出品牌价值主张
- **信任指标**: 50万+用户、99.9%成功率等
- **限时优惠**: 倒计时抢购活动
- **热门产品**: 精选Steam钱包、游戏、道具
- **产品分类**: 四大主要产品类别
- **用户评价**: 真实用户反馈和评分
- **合作伙伴**: 展示权威合作关系

### 🛍️ 产品商城 (products.html)
- **搜索功能**: 产品名称快速搜索
- **筛选系统**: 按分类、价格、排序筛选
- **产品卡片**: 精美的产品展示卡片
- **折扣标签**: 醒目的优惠信息
- **加载更多**: 分页加载更多产品
- **购买流程**: 一键加入购物车

### 💳 Steam钱包充值 (wallet.html)
- **金额选择**: 预设金额卡片（¥20-¥500）
- **自定义金额**: 支持¥10-¥5000自定义
- **Steam信息**: 账户信息填写表单
- **支付方式**: 支付宝、微信、银行卡等
- **订单摘要**: 实时计算费用明细
- **优惠券系统**: 多种优惠券可选
- **常见问题**: 充值相关FAQ

### 🔐 用户认证
#### 登录页面 (login.html)
- **登录表单**: 用户名/邮箱 + 密码
- **记住我**: 保持登录状态选项
- **第三方登录**: Steam、QQ、微信登录
- **密码找回**: 忘记密码重置链接

#### 注册页面 (register.html)
- **详细表单**: 完整的用户信息收集
- **密码强度**: 实时密码强度检测
- **Steam ID**: 可选的Steam账户关联
- **服务条款**: 用户协议和隐私政策
- **新用户福利**: 注册奖励展示

### 📦 订单管理 (orders.html)
- **订单统计**: 总订单数、进行中、已完成等
- **状态筛选**: 按订单状态快速筛选
- **搜索功能**: 订单号、商品名称搜索
- **订单详情**: 完整的订单信息展示
- **配送信息**: 充值码、激活码展示
- **操作按钮**: 取消、支付、评价等操作

### 👤 个人中心 (profile.html)
#### 账户概览
- **统计数据**: 消费金额、订单数量、积分等
- **近期活动**: 最新的交易和操作记录

#### 个人信息
- **基本信息**: 姓名、邮箱、手机等
- **Steam信息**: Steam ID、等级等

#### 安全设置
- **密码管理**: 修改登录密码
- **两步验证**: 手机/邮箱验证设置
- **登录记录**: 历史登录信息查看

#### 偏好设置
- **通知设置**: 邮件、短信通知开关
- **语言地区**: 显示语言、货币单位

#### 交易记录
- **历史记录**: 完整的交易历史
- **筛选功能**: 按类型、时间筛选

#### 积分奖励
- **积分余额**: 当前可用积分
- **兑换商城**: 积分兑换优惠券
- **积分记录**: 获得和消费记录

### 🎧 客服支持 (support.html)
#### 快速帮助
- **问题分类**: 充值、订单、安全、游戏四大类
- **快速导航**: 直达相关FAQ解答

#### 联系客服
- **在线客服**: 实时对话功能
- **电话客服**: 400热线电话
- **邮箱支持**: 详细问题邮件咨询
- **QQ客服**: 即时通讯支持

#### 工单系统
- **问题提交**: 详细的工单提交表单
- **优先级选择**: 紧急、较急、一般
- **响应承诺**: 不同优先级的响应时间

#### FAQ问答
- **分类展示**: 按问题类型组织
- **可展开式**: 点击查看详细解答
- **覆盖全面**: 充值、订单、安全、游戏等

#### 服务承诺
- **快速响应**: 7x24小时在线服务
- **安全保障**: 资金安全、隐私保护
- **满意服务**: 99.5%客户满意度
- **品质承诺**: 7年行业经验

## 🎯 核心功能

### JavaScript功能
- **用户认证管理**: 登录状态管理、权限控制
- **购物车系统**: 商品添加、数量管理、总价计算
- **搜索和筛选**: 实时搜索、多条件筛选
- **表单验证**: 用户输入验证、错误提示
- **消息系统**: 成功、错误、信息提示
- **动画效果**: 倒计时、进度条、加载动画
- **数据持久化**: LocalStorage数据存储

### CSS特性
- **Steam主题色彩**: --steam-blue, --steam-green等CSS变量
- **响应式网格**: CSS Grid和Flexbox布局
- **现代按钮系统**: 多种样式和尺寸的按钮
- **卡片组件**: 统一的卡片设计语言
- **表单样式**: 优雅的表单控件样式
- **动画过渡**: 流畅的悬停和交互效果

## 🌐 浏览器兼容性

- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 11+
- ✅ Edge 79+
- ✅ 移动端浏览器

## 📱 响应式设计

- **桌面端**: 1200px+ 完整功能布局
- **平板端**: 768px-1199px 自适应调整
- **手机端**: <768px 移动优化布局

## 🔧 自定义配置

### 修改主题色彩
在 `styles/main.css` 中修改CSS变量：
```css
:root {
    --steam-blue: #1b2838;
    --steam-green: #90ba3c;
    --steam-orange: #ff6600;
    /* 添加你的自定义颜色 */
}
```

### 添加新页面
1. 创建新的HTML文件
2. 在导航栏中添加链接
3. 添加对应的CSS样式
4. 实现页面JavaScript功能

## 🚀 部署建议

### 静态网站托管
- **GitHub Pages**: 免费静态网站托管
- **Netlify**: 自动化部署和CDN
- **Vercel**: 快速部署和全球CDN
- **Firebase Hosting**: Google云托管服务

### 本地服务器
```bash
# Python
python -m http.server 8000

# Node.js
npx serve .

# PHP
php -S localhost:8000
```

## 📈 性能优化

- **图片优化**: 使用WebP格式和压缩
- **CSS优化**: 合并和压缩CSS文件
- **JavaScript优化**: 代码分割和懒加载
- **缓存策略**: 设置适当的缓存头
- **CDN加速**: 使用CDN分发静态资源

## 🛡️ 安全考虑

- **输入验证**: 前端表单验证
- **HTTPS**: 生产环境使用HTTPS
- **CSP头部**: 内容安全策略
- **XSS防护**: 避免innerHTML注入
- **数据加密**: 敏感信息本地加密

## 🤝 贡献指南

1. Fork项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 📄 许可证

MIT License - 详见LICENSE文件

## 📞 支持和反馈

如有问题或建议，请联系：
- Email: <EMAIL>
- QQ群: 123456789
- GitHub Issues: 提交问题和建议

---

**享受你的Steam游戏交易体验！** 🎮 