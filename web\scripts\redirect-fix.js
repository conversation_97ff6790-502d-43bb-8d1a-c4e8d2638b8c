/**
 * 页面跳转工具函数
 * 解决各种跳转问题的通用解决方案
 */

// 强制页面跳转函数
function forceRedirect(url, delay = 1000) {
    console.log(`准备在 ${delay}ms 后跳转到: ${url}`);
    
    setTimeout(() => {
        console.log(`开始跳转到: ${url}`);
        
        // 方法1: 使用 replace 避免历史记录
        try {
            window.location.replace(url);
            return;
        } catch (e) {
            console.warn('replace方法失败:', e);
        }
        
        // 方法2: 使用 assign
        try {
            window.location.assign(url);
            return;
        } catch (e) {
            console.warn('assign方法失败:', e);
        }
        
        // 方法3: 直接设置 href
        try {
            window.location.href = url;
            return;
        } catch (e) {
            console.warn('href方法失败:', e);
        }
        
        // 方法4: 强制刷新页面
        try {
            window.top.location.href = url;
            return;
        } catch (e) {
            console.warn('top.location方法失败:', e);
        }
        
        // 最后的备用方案：使用JavaScript创建一个隐藏的链接并点击
        try {
            const link = document.createElement('a');
            link.href = url;
            link.style.display = 'none';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        } catch (e) {
            console.error('所有跳转方法都失败了:', e);
            alert(`跳转失败，请手动访问: ${url}`);
        }
    }, delay);
}

// 智能跳转函数 - 会记住用户来源页面
function smartRedirect(targetUrl, fallbackUrl = '/') {
    // 获取来源页面
    const referrer = document.referrer;
    const returnUrl = new URLSearchParams(window.location.search).get('return_url');
    
    let redirectUrl = targetUrl;
    
    // 如果有return_url参数，优先使用
    if (returnUrl && returnUrl.startsWith('/')) {
        redirectUrl = returnUrl;
        console.log('使用return_url跳转:', redirectUrl);
    } 
    // 如果有有效的来源页面，且不是登录/注册页面
    else if (referrer && 
             !referrer.includes('/login') && 
             !referrer.includes('/register') &&
             referrer.includes(window.location.origin)) {
        // 从完整URL中提取路径
        try {
            const referrerUrl = new URL(referrer);
            redirectUrl = referrerUrl.pathname;
            console.log('使用referrer跳转:', redirectUrl);
        } catch (e) {
            console.warn('解析referrer失败:', e);
        }
    }
    
    console.log(`智能跳转: ${redirectUrl}`);
    forceRedirect(redirectUrl, 1500);
}

// 导出函数供全局使用
window.forceRedirect = forceRedirect;
window.smartRedirect = smartRedirect; 