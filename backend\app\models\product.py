from sqlalchemy import Column, String, Boolean, DateTime, Integer, Enum, Text, DECIMAL, ForeignKey
from sqlalchemy.orm import relationship
from .base import BaseModel
import enum


class ProductType(str, enum.Enum):
    """产品类型枚举"""
    STEAM_GAME = "steam_game"
    CDK = "cdk"
    STEAM_BALANCE = "steam_balance"
    GIFT_CARD = "gift_card"


class ProductStatus(str, enum.Enum):
    """产品状态枚举"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    SOLD_OUT = "sold_out"
    DISCONTINUED = "discontinued"


class OrderStatus(str, enum.Enum):
    """订单状态枚举"""
    PENDING = "pending"
    PAID = "paid"
    PROCESSING = "processing"
    COMPLETED = "completed"
    CANCELLED = "cancelled"
    REFUNDED = "refunded"


class OrderType(str, enum.Enum):
    """订单类型枚举"""
    BUY = "buy"
    SELL = "sell"


class Product(BaseModel):
    """产品模型"""
    __tablename__ = "products"

    # 基本信息
    name = Column(String(200), nullable=False, index=True)
    name_en = Column(String(200), nullable=True)
    name_ru = Column(String(200), nullable=True)
    name_tr = Column(String(200), nullable=True)
    
    description = Column(Text, nullable=True)
    short_description = Column(String(500), nullable=True)
    
    # 产品属性
    product_type = Column(Enum(ProductType), nullable=False, index=True)
    status = Column(Enum(ProductStatus), default=ProductStatus.ACTIVE, nullable=False)
    
    # Steam相关
    steam_app_id = Column(String(20), nullable=True, index=True)
    steam_app_name = Column(String(200), nullable=True)
    
    # 价格信息
    original_price = Column(DECIMAL(10, 2), nullable=True)
    selling_price = Column(DECIMAL(10, 2), nullable=False)
    discount_percentage = Column(Integer, default=0, nullable=False)
    
    # 库存信息
    stock_quantity = Column(Integer, default=0, nullable=False)
    sold_quantity = Column(Integer, default=0, nullable=False)
    
    # 图片和媒体
    cover_image_url = Column(String(500), nullable=True)
    thumbnail_url = Column(String(500), nullable=True)
    screenshots = Column(Text, nullable=True)  # JSON格式存储
    
    # 分类和标签
    category = Column(String(50), nullable=True, index=True)
    tags = Column(Text, nullable=True)  # JSON格式存储
    
    # 地区限制
    region_restriction = Column(String(500), nullable=True)  # JSON格式存储支持的地区
    
    # 卖家信息
    seller_id = Column(Integer, ForeignKey("users.id"), nullable=True, index=True)
    
    # 评分和评论
    rating = Column(DECIMAL(3, 2), default=0.00, nullable=False)
    review_count = Column(Integer, default=0, nullable=False)
    
    # 其他属性
    is_featured = Column(Boolean, default=False, nullable=False)
    is_hot = Column(Boolean, default=False, nullable=False)
    sort_order = Column(Integer, default=0, nullable=False)
    
    def __repr__(self):
        return f"<Product(id={self.id}, name='{self.name}', type='{self.product_type}')>"


class Order(BaseModel):
    """订单模型"""
    __tablename__ = "orders"

    # 订单基本信息
    order_number = Column(String(32), unique=True, nullable=False, index=True)
    order_type = Column(Enum(OrderType), nullable=False)
    status = Column(Enum(OrderStatus), default=OrderStatus.PENDING, nullable=False, index=True)
    
    # 用户信息
    buyer_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    seller_id = Column(Integer, ForeignKey("users.id"), nullable=True, index=True)
    
    # 产品信息
    product_id = Column(Integer, ForeignKey("products.id"), nullable=True, index=True)
    product_name = Column(String(200), nullable=False)
    product_type = Column(Enum(ProductType), nullable=False)
    
    # 价格信息
    unit_price = Column(DECIMAL(10, 2), nullable=False)
    quantity = Column(Integer, default=1, nullable=False)
    total_amount = Column(DECIMAL(10, 2), nullable=False)
    discount_amount = Column(DECIMAL(10, 2), default=0.00, nullable=False)
    final_amount = Column(DECIMAL(10, 2), nullable=False)
    
    # Steam相关信息
    steam_trade_offer_id = Column(String(20), nullable=True)
    steam_app_id = Column(String(20), nullable=True)
    
    # CDK相关信息
    cdk_codes = Column(Text, nullable=True)  # JSON格式存储CDK码
    
    # 支付信息
    payment_method = Column(String(50), nullable=True)
    payment_transaction_id = Column(String(100), nullable=True)
    paid_at = Column(DateTime(timezone=True), nullable=True)
    
    # 交付信息
    delivery_method = Column(String(50), nullable=True)
    delivery_info = Column(Text, nullable=True)  # JSON格式存储交付信息
    delivered_at = Column(DateTime(timezone=True), nullable=True)
    
    # 时间信息
    expires_at = Column(DateTime(timezone=True), nullable=True)
    completed_at = Column(DateTime(timezone=True), nullable=True)
    cancelled_at = Column(DateTime(timezone=True), nullable=True)
    
    # 备注信息
    buyer_notes = Column(Text, nullable=True)
    seller_notes = Column(Text, nullable=True)
    admin_notes = Column(Text, nullable=True)
    
    def __repr__(self):
        return f"<Order(id={self.id}, order_number='{self.order_number}', status='{self.status}')>"


class CDKInventory(BaseModel):
    """CDK库存模型"""
    __tablename__ = "cdk_inventory"
    
    # 基本信息
    product_id = Column(Integer, ForeignKey("products.id"), nullable=False, index=True)
    cdk_code = Column(String(100), nullable=False, unique=True, index=True)
    
    # 状态信息
    is_used = Column(Boolean, default=False, nullable=False)
    is_reserved = Column(Boolean, default=False, nullable=False)
    
    # 使用信息
    used_by_user_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    used_in_order_id = Column(Integer, ForeignKey("orders.id"), nullable=True)
    used_at = Column(DateTime(timezone=True), nullable=True)
    
    # 供应商信息
    supplier_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    supplier_notes = Column(Text, nullable=True)
    
    def __repr__(self):
        return f"<CDKInventory(id={self.id}, product_id={self.product_id}, is_used={self.is_used})>" 