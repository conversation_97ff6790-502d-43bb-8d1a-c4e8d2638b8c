<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户注册 - SteamPY</title>
    <link rel="stylesheet" href="/static/main.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .register-container {
            min-height: 100vh;
            background: linear-gradient(135deg, var(--steam-blue) 0%, var(--steam-lightblue) 100%);
            padding: 40px 20px;
        }
        .register-box {
            background: white;
            border-radius: 16px;
            padding: 40px;
            width: 100%;
            max-width: 500px;
            margin: 0 auto;
            box-shadow: 0 20px 60px rgba(0,0,0,0.2);
        }
        .register-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .register-title {
            font-size: 2rem;
            margin-bottom: 10px;
            color: #2c3e50;
        }
        .register-subtitle {
            color: #6c757d;
        }
        .benefits {
            background: linear-gradient(45deg, rgba(144, 186, 60, 0.1), rgba(144, 186, 60, 0.05));
            border: 1px solid rgba(144, 186, 60, 0.3);
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
        }
        .benefits h3 {
            color: var(--steam-green);
            margin-bottom: 15px;
            font-size: 1.1rem;
        }
        .benefits-list {
            list-style: none;
            padding: 0;
        }
        .benefits-list li {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 8px;
            color: #2c3e50;
        }
        .benefits-list li::before {
            content: '✓';
            color: var(--steam-green);
            font-weight: bold;
        }
        .form-row {
            display: flex;
            gap: 15px;
        }
        .form-row .form-group {
            flex: 1;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
        }
        .form-input {
            width: 100%;
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }
        .form-input:focus {
            outline: none;
            border-color: var(--steam-green);
        }
        .form-input.error {
            border-color: #e74c3c;
        }
        .error-message {
            color: #e74c3c;
            font-size: 14px;
            margin-top: 5px;
            display: none;
        }
        .password-strength {
            margin-top: 8px;
        }
        .strength-bar {
            height: 4px;
            background: #eee;
            border-radius: 2px;
            overflow: hidden;
        }
        .strength-fill {
            height: 100%;
            transition: all 0.3s ease;
            border-radius: 2px;
        }
        .strength-text {
            font-size: 12px;
            margin-top: 5px;
        }
        .agreements {
            margin-bottom: 30px;
        }
        .agreement-item {
            display: flex;
            align-items: flex-start;
            gap: 10px;
            margin-bottom: 12px;
            font-size: 14px;
        }
        .agreement-item input[type="checkbox"] {
            margin-top: 2px;
        }
        .agreement-item a {
            color: var(--steam-green);
            text-decoration: none;
        }
        .agreement-item a:hover {
            text-decoration: underline;
        }
        .register-btn {
            width: 100%;
            margin-bottom: 20px;
        }
        .login-prompt {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }
        .login-link {
            color: var(--steam-green);
            text-decoration: none;
            font-weight: 600;
        }
        .login-link:hover {
            text-decoration: underline;
        }
        .back-home {
            position: absolute;
            top: 20px;
            left: 20px;
            color: white;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 10px 15px;
            border-radius: 8px;
            background: rgba(255,255,255,0.1);
            transition: background 0.3s ease;
        }
        .back-home:hover {
            background: rgba(255,255,255,0.2);
            color: white;
        }
    </style>
</head>
<body>
    <div class="register-container">
        <a href="index.html" class="back-home">
            <i class="fas fa-arrow-left"></i>
            返回首页
        </a>
        
        <div class="register-box">
            <div class="register-header">
                <h1 class="register-title">创建账户</h1>
                <p class="register-subtitle">加入SteamPY，开启游戏交易之旅</p>
            </div>
            
            <div class="benefits">
                <h3>🎁 新用户专享福利</h3>
                <ul class="benefits-list">
                    <li>注册即送¥10优惠券</li>
                    <li>首次充值额外赠送5%</li>
                    <li>专属新手客服指导</li>
                    <li>VIP快速通道服务</li>
                </ul>
            </div>
            
            <form id="register-form">
                <div class="form-row">
                    <div class="form-group">
                        <label for="firstName">姓</label>
                        <input type="text" id="firstName" class="form-input" placeholder="请输入姓氏" required>
                        <div class="error-message" id="firstName-error"></div>
                    </div>
                    <div class="form-group">
                        <label for="lastName">名</label>
                        <input type="text" id="lastName" class="form-input" placeholder="请输入名字" required>
                        <div class="error-message" id="lastName-error"></div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="username">用户名</label>
                    <input type="text" id="username" class="form-input" placeholder="请输入用户名" required>
                    <div class="error-message" id="username-error"></div>
                </div>
                
                <div class="form-group">
                    <label for="email">邮箱地址</label>
                    <input type="email" id="email" class="form-input" placeholder="请输入邮箱地址" required>
                    <div class="error-message" id="email-error"></div>
                </div>
                
                <div class="form-group">
                    <label for="phone">手机号码</label>
                    <input type="tel" id="phone" class="form-input" placeholder="请输入手机号码" required>
                    <div class="error-message" id="phone-error"></div>
                </div>
                
                <div class="form-group">
                    <label for="password">密码</label>
                    <input type="password" id="password" class="form-input" placeholder="请输入密码" required>
                    <div class="password-strength">
                        <div class="strength-bar">
                            <div class="strength-fill" id="strength-fill"></div>
                        </div>
                        <div class="strength-text" id="strength-text">密码强度：请输入密码</div>
                    </div>
                    <div class="error-message" id="password-error"></div>
                </div>
                
                <div class="form-group">
                    <label for="confirmPassword">确认密码</label>
                    <input type="password" id="confirmPassword" class="form-input" placeholder="请再次输入密码" required>
                    <div class="error-message" id="confirmPassword-error"></div>
                </div>
                
                <div class="form-group">
                    <label for="steamId">Steam ID（可选）</label>
                    <input type="text" id="steamId" class="form-input" placeholder="方便为您提供更好的服务">
                    <div class="error-message" id="steamId-error"></div>
                </div>
                
                <div class="agreements">
                    <div class="agreement-item">
                        <input type="checkbox" id="terms" required>
                        <label for="terms">我已阅读并同意 <a href="#">用户服务协议</a> 和 <a href="#">隐私政策</a></label>
                    </div>
                    <div class="agreement-item">
                        <input type="checkbox" id="marketing">
                        <label for="marketing">我同意接收SteamPY的营销邮件和优惠信息</label>
                    </div>
                </div>
                
                <button type="submit" class="btn btn-primary register-btn">创建账户</button>
            </form>
            
            <div class="login-prompt">
                <p>已有账户？<a href="/login" class="login-link">立即登录</a></p>
            </div>
        </div>
    </div>

    <script src="/scripts/main.js"></script>
    <script src="/scripts/redirect-fix.js"></script>
    <script>
        // 备用showMessage函数，防止main.js加载失败
        if (typeof window.showMessage === 'undefined') {
            window.showMessage = function(message, type = 'info') {
                const messageDiv = document.createElement('div');
                messageDiv.textContent = message;
                messageDiv.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    padding: 15px 20px;
                    background: ${type === 'success' ? '#2ecc71' : type === 'error' ? '#e74c3c' : '#3498db'};
                    color: white;
                    border-radius: 5px;
                    z-index: 1000;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                `;
                document.body.appendChild(messageDiv);
                setTimeout(() => messageDiv.remove(), 3000);
            };
        }
        
        document.addEventListener('DOMContentLoaded', function() {
            const registerForm = document.getElementById('register-form');
            const passwordInput = document.getElementById('password');
            const confirmPasswordInput = document.getElementById('confirmPassword');
            const strengthFill = document.getElementById('strength-fill');
            const strengthText = document.getElementById('strength-text');
            
            // 密码强度检测
            passwordInput.addEventListener('input', function() {
                const password = this.value;
                const strength = calculatePasswordStrength(password);
                updatePasswordStrength(strength);
            });
            
            function calculatePasswordStrength(password) {
                let score = 0;
                
                if (password.length >= 8) score += 20;
                if (password.length >= 12) score += 10;
                if (/[a-z]/.test(password)) score += 20;
                if (/[A-Z]/.test(password)) score += 20;
                if (/[0-9]/.test(password)) score += 20;
                if (/[^A-Za-z0-9]/.test(password)) score += 10;
                
                return Math.min(score, 100);
            }
            
            function updatePasswordStrength(strength) {
                const colors = {
                    0: { color: '#e74c3c', text: '密码太弱' },
                    30: { color: '#f39c12', text: '密码较弱' },
                    60: { color: '#f1c40f', text: '密码一般' },
                    80: { color: '#27ae60', text: '密码较强' },
                    100: { color: '#2ecc71', text: '密码很强' }
                };
                
                let level = 0;
                for (const threshold in colors) {
                    if (strength >= threshold) level = threshold;
                }
                
                strengthFill.style.width = strength + '%';
                strengthFill.style.backgroundColor = colors[level].color;
                strengthText.textContent = '密码强度：' + colors[level].text;
                strengthText.style.color = colors[level].color;
            }
            
            // 表单验证
            function validateField(fieldId, validator, errorMessage) {
                const field = document.getElementById(fieldId);
                const errorElement = document.getElementById(fieldId + '-error');
                
                const isValid = validator(field.value);
                
                if (isValid) {
                    field.classList.remove('error');
                    errorElement.style.display = 'none';
                } else {
                    field.classList.add('error');
                    errorElement.textContent = errorMessage;
                    errorElement.style.display = 'block';
                }
                
                return isValid;
            }
            
            // 验证规则
            const validators = {
                firstName: (value) => value.trim().length > 0,
                lastName: (value) => value.trim().length > 0,
                username: (value) => /^[a-zA-Z0-9_]{3,20}$/.test(value),
                email: (value) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value),
                phone: (value) => /^1[3-9]\d{9}$/.test(value),
                password: (value) => value.length >= 6,
                confirmPassword: (value) => value === passwordInput.value,
                steamId: (value) => true // 可选字段
            };
            
            const errorMessages = {
                firstName: '请输入有效的姓氏',
                lastName: '请输入有效的名字',
                username: '用户名只能包含字母、数字和下划线，长度3-20位',
                email: '请输入有效的邮箱地址',
                phone: '请输入有效的手机号码',
                password: '密码至少需要6位字符',
                confirmPassword: '两次输入的密码不一致',
                steamId: ''
            };
            
            // 实时验证
            Object.keys(validators).forEach(fieldId => {
                const field = document.getElementById(fieldId);
                if (field) {
                    field.addEventListener('blur', () => {
                        validateField(fieldId, validators[fieldId], errorMessages[fieldId]);
                    });
                }
            });
            
            // 表单提交
            registerForm.addEventListener('submit', async function(e) {
                e.preventDefault();
                
                let isValid = true;
                
                // 验证所有字段
                Object.keys(validators).forEach(fieldId => {
                    if (!validateField(fieldId, validators[fieldId], errorMessages[fieldId])) {
                        isValid = false;
                    }
                });
                
                // 检查协议同意
                const termsChecked = document.getElementById('terms').checked;
                if (!termsChecked) {
                    showMessage('请同意用户服务协议和隐私政策', 'error');
                    isValid = false;
                }
                
                if (!isValid) {
                    showMessage('请检查并修正表单中的错误', 'error');
                    return;
                }
                
                // 发送真实注册请求
                const registerBtn = this.querySelector('.register-btn');
                registerBtn.textContent = '注册中...';
                registerBtn.disabled = true;
                
                try {
                    const formData = {
                        username: document.getElementById('username').value,
                        email: document.getElementById('email').value,
                        password: document.getElementById('password').value,
                        confirm_password: document.getElementById('confirmPassword').value
                    };
                    
                    console.log('发送注册请求:', formData);
                    
                    const response = await fetch('/api/v1/auth/register', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(formData)
                    });
                    
                    const result = await response.json();
                    console.log('注册响应:', result);
                    
                    if (response.ok && result.success) {
                        showMessage('注册成功！欢迎加入SteamPY！正在跳转到登录页面...', 'success');
                        
                        // 使用新的强制跳转函数
                        if (typeof window.forceRedirect === 'function') {
                            window.forceRedirect('/login', 2000);
                        } else {
                            // 备用方案
                            setTimeout(() => {
                                console.log('使用备用跳转方案...');
                                window.location.replace('/login');
                            }, 2000);
                        }
                    } else {
                        // 处理错误
                        const errorMessage = result.detail || result.message || '注册失败，请重试';
                        showMessage(errorMessage, 'error');
                        console.error('注册失败:', result);
                    }
                } catch (error) {
                    console.error('注册请求失败:', error);
                    showMessage('网络错误，请检查网络连接后重试', 'error');
                } finally {
                    // 恢复按钮状态
                    registerBtn.textContent = '创建账户';
                    registerBtn.disabled = false;
                }
            });
        });
    </script>
</body>
</html> 