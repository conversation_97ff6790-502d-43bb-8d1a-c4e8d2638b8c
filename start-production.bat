@echo off
echo 🏭 启动 SteamPY 项目 (生产模式)...

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python 未安装，请先安装 Python 3.11+
    pause
    exit /b 1
)

REM 检查Node.js是否安装
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js 未安装，请先安装 Node.js 18+
    pause
    exit /b 1
)

echo 📋 生产模式启动...

REM 切换到后端目录
cd backend

REM 创建虚拟环境（如果不存在）
if not exist "venv" (
    echo 📦 创建Python虚拟环境...
    python -m venv venv
)

REM 激活虚拟环境
echo 🔧 激活虚拟环境...
call venv\Scripts\activate.bat

REM 安装依赖
echo 📦 安装后端依赖...
pip install -r requirements.txt

REM 启动后端服务
echo 🚀 启动后端服务...
start "SteamPY Backend" cmd /k "python main.py"

REM 等待一下让后端启动
timeout /t 5 /nobreak >nul

REM 切换到前端目录
cd ..\frontend

REM 安装前端依赖
echo 📦 安装前端依赖...
npm install

REM 构建前端项目
echo 🔨 构建前端项目...
npm run build

if errorlevel 1 (
    echo ❌ 前端构建失败，请检查错误信息
    pause
    exit /b 1
)

REM 启动前端生产服务器
echo 🚀 启动前端生产服务器...
start "SteamPY Frontend Production" cmd /k "npm start"

echo.
echo ✅ 项目启动完成！(生产模式)
echo 🌐 前端地址: http://localhost:3000
echo 🔗 后端API: http://localhost:8000
echo 📚 API文档: http://localhost:8000/docs
echo 🗄️  数据库: SQLite (backend\steampy.db)
echo.
echo 📋 默认管理员账户:
echo    用户名: admin
echo    密码: admin123
echo.
echo 💡 生产模式说明:
echo    - 前端已预编译，性能更佳
echo    - 不支持热重载
echo    - 适用于生产环境部署
echo.
echo ⏹️  关闭窗口即可停止对应服务
pause 