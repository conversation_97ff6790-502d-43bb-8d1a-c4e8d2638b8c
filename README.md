# SteamPY - Steam游戏交易平台

一个现代化的Steam游戏交易平台，提供Steam余额交易、CDKey交易、游戏代购等服务。

## 技术栈

### 后端 (Python)
- **FastAPI** - 现代化API框架
- **SQLAlchemy** - ORM数据库操作
- **PostgreSQL** - 主数据库
- **Redis** - 缓存和会话存储
- **Celery** - 异步任务处理
- **JWT** - 用户认证
- **Pydantic** - 数据验证

### 前端 (Next.js)
- **Next.js 14** - React全栈框架
- **TypeScript** - 类型安全
- **Tailwind CSS** - 样式框架
- **ShadcN UI** - 组件库
- **Zustand** - 状态管理
- **React Query** - 数据获取和缓存
- **Next-i18n** - 国际化

## 核心功能

### 用户系统
- 用户注册/登录
- Steam账号绑定
- 用户认证和权限管理
- 多语言支持（中文、英文、俄文、土耳其文）

### 交易系统
- Steam余额买卖
- CDKey交易
- Steam游戏代购
- 订单管理
- 支付系统集成

### 管理系统
- 用户管理
- 订单管理
- 财务管理
- 系统配置

## 项目结构

```
steampy-platform/
├── backend/                 # Python后端
│   ├── app/
│   │   ├── api/            # API路由
│   │   ├── core/           # 核心配置
│   │   ├── models/         # 数据模型
│   │   ├── services/       # 业务逻辑
│   │   └── utils/          # 工具函数
│   ├── requirements.txt
│   └── main.py
├── frontend/               # Next.js前端
│   ├── src/
│   │   ├── app/           # App Router
│   │   ├── components/    # 组件
│   │   ├── lib/          # 工具库
│   │   └── types/        # TypeScript类型
│   ├── package.json
│   └── next.config.js
├── docker-compose.yml      # Docker配置
└── README.md
```

## 快速开始

### 环境要求
- Python 3.11+
- Node.js 18.17.0+ (推荐) 或 16.14.0+ (兼容版本)
- SQLite (内置，无需额外安装)

#### Node.js 版本说明
- **推荐**: Node.js 18.17.0+ 使用 Next.js 14
- **兼容**: Node.js 16.14.0+ 使用 Next.js 13 (当前配置，已优化)
- **升级方法**: 访问 [Node.js官网](https://nodejs.org/) 下载最新LTS版本
- 如果您的Node.js版本低于16，必须升级后才能运行前端

#### 兼容性修复
当前项目已针对Node.js 16进行优化：
- ✅ 移除了不兼容的 `next-intl` 包
- ✅ 简化了 Next.js 配置
- ✅ 降级了部分依赖包版本
- ✅ 测试通过，可正常启动

### 安装步骤

#### 方式一：Docker 启动 (推荐)
```bash
# 确保Docker已安装并运行
chmod +x start.sh
./start.sh
```

#### 方式二：本地开发启动

**Linux/Mac:**
```bash
# 使用本地启动脚本
chmod +x start-local.sh
./start-local.sh
```

**Windows:**
```cmd
# 开发模式（推荐）
start-windows.bat

# 生产模式
start-production.bat
```

#### 方式三：手动启动

**开发模式（推荐）：**

*Linux/Mac:*
```bash
# 1. 启动后端
cd backend
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt
python main.py

# 2. 启动前端 (新终端)
cd frontend
npm install
npm run dev  # 开发服务器，支持热重载
```

*Windows:*
```cmd
REM 1. 启动后端
cd backend
python -m venv venv
venv\Scripts\activate
pip install -r requirements.txt
python main.py

REM 2. 启动前端 (新命令行窗口)
cd frontend
npm install
npm run dev
```

**生产模式：**

*Linux/Mac:*
```bash
# 1. 启动后端（同上）

# 2. 构建并启动前端
cd frontend
npm install
npm run build  # 构建生产版本
npm start      # 启动生产服务器
```

*Windows:*
```cmd
REM 1. 启动后端（同上）

REM 2. 构建并启动前端
cd frontend
npm install
npm run build
npm start
```

## API文档

后端API文档将在 `http://localhost:8000/docs` 提供完整的Swagger文档。

## 📊 默认账户

项目首次启动时会自动创建管理员账户：
- **用户名**: admin
- **密码**: admin123
- **权限**: 管理员

## 📁 数据库

项目使用SQLite作为内置数据库，数据文件位于：
- `backend/steampy.db`

## 🔧 API测试

启动后可以访问以下地址：
- **API文档**: http://localhost:8000/docs
- **健康检查**: http://localhost:8000/health
- **用户注册**: POST http://localhost:8000/api/v1/auth/register
- **用户登录**: POST http://localhost:8000/api/v1/auth/login

## 部署

### Docker部署
```bash
# 使用Docker Compose
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

### 本地部署
项目支持本地直接运行，无需外部数据库依赖。 