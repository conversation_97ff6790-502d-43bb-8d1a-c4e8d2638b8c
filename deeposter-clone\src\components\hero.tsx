import { But<PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

export function Hero() {
  return (
    <section className="relative pt-28 pb-20 grid-background hero-gradient">
      <div className="container mx-auto px-4 text-center">
        <h1 className="text-5xl md:text-6xl lg:text-7xl font-bold tracking-tight mb-6">
          Generate AI Poster
          <br />
          <span className="text-4xl md:text-5xl lg:text-6xl">in a Click.</span>
        </h1>
        <p className="text-lg text-muted-foreground max-w-3xl mx-auto mb-10">
          Transform your ideas into eye-catching posters with our AI Poster Generator. No design
          skills required. Try our AI Poster Generator and unleash your creativity!
        </p>
        <Button size="lg" className="rounded-full px-8 py-6 h-auto text-lg">
          <span className="mr-2">✨</span> Start for free
        </Button>

        <div className="mt-10 flex flex-col items-center">
          <div className="flex -space-x-2 overflow-hidden mb-2">
            <Avatar className="border-2 border-background w-8 h-8">
              <AvatarImage src="/images/avatar1.png" alt="User" />
              <AvatarFallback>U1</AvatarFallback>
            </Avatar>
            <Avatar className="border-2 border-background w-8 h-8">
              <AvatarImage src="/images/avatar2.png" alt="User" />
              <AvatarFallback>U2</AvatarFallback>
            </Avatar>
            <Avatar className="border-2 border-background w-8 h-8">
              <AvatarImage src="/images/avatar3.png" alt="User" />
              <AvatarFallback>U3</AvatarFallback>
            </Avatar>
            <Avatar className="border-2 border-background w-8 h-8">
              <AvatarImage src="/images/avatar4.png" alt="User" />
              <AvatarFallback>U4</AvatarFallback>
            </Avatar>
          </div>
          <p className="text-sm text-muted-foreground">
            2000+ creators already joined DeePoster!
          </p>
        </div>
      </div>
    </section>
  );
}
