version: '3.8'

services:
  # Python后端
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: steampy-backend
    environment:
      - DATABASE_URL=sqlite:///./steampy.db
      - DEBUG=true
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
      - backend_data:/app/data  # 持久化SQLite数据库
    restart: unless-stopped
    command: uvicorn main:app --host 0.0.0.0 --port 8000 --reload

  # Next.js前端
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: steampy-frontend
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:8000
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      - backend
    restart: unless-stopped
    command: npm run dev

volumes:
  backend_data:

networks:
  default:
    name: steampy-network 