#!/usr/bin/env python3
"""
批量更新HTML文件中的导航链接，从静态文件链接改为后端路由
"""

import os
import re

def update_html_links():
    web_dir = "web"
    
    # 定义需要替换的链接映射
    link_mappings = {
        'href="index.html"': 'href="/"',
        'href="products.html"': 'href="/products"',
        'href="wallet.html"': 'href="/wallet"',
        'href="login.html"': 'href="/login"',
        'href="register.html"': 'href="/register"',
        'href="orders.html"': 'href="/orders"',
        'href="profile.html"': 'href="/profile"',
        'href="support.html"': 'href="/support"',
    }
    
    # 获取所有HTML文件
    html_files = []
    for file in os.listdir(web_dir):
        if file.endswith('.html'):
            html_files.append(os.path.join(web_dir, file))
    
    print(f"找到 {len(html_files)} 个HTML文件")
    
    # 更新每个文件
    for file_path in html_files:
        print(f"更新 {file_path}")
        
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 应用所有替换
        updated_content = content
        for old_link, new_link in link_mappings.items():
            updated_content = updated_content.replace(old_link, new_link)
        
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(updated_content)
        
        print(f"✅ {file_path} 更新完成")
    
    print("🎉 所有文件链接更新完成！")

if __name__ == "__main__":
    update_html_links() 