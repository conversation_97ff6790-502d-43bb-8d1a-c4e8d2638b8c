<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的订单 - SteamPY</title>
    <link rel="stylesheet" href="/static/main.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .orders-section {
            padding: 60px 0;
            background: #f8f9fa;
            min-height: 70vh;
        }
        .orders-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 40px;
        }
        .orders-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        .stat-card {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            text-align: center;
        }
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: var(--steam-green);
            margin-bottom: 8px;
        }
        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
        }
        .orders-filter {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        .filter-tabs {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }
        .filter-tab {
            padding: 10px 20px;
            border: 2px solid #ddd;
            border-radius: 8px;
            background: white;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .filter-tab.active {
            border-color: var(--steam-green);
            background: rgba(144, 186, 60, 0.1);
            color: var(--steam-green);
        }
        .filter-row {
            display: flex;
            gap: 20px;
            align-items: center;
        }
        .filter-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .filter-group select, .filter-group input {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .orders-list {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        .order-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            overflow: hidden;
            transition: transform 0.3s ease;
        }
        .order-card:hover {
            transform: translateY(-2px);
        }
        .order-header {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .order-info {
            display: flex;
            gap: 30px;
            align-items: center;
        }
        .order-id {
            font-weight: bold;
            color: #2c3e50;
        }
        .order-date {
            color: #6c757d;
            font-size: 0.9rem;
        }
        .order-status {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
        .status-processing {
            background: #d4edda;
            color: #155724;
        }
        .status-completed {
            background: #d1ecf1;
            color: #0c5460;
        }
        .status-failed {
            background: #f8d7da;
            color: #721c24;
        }
        .order-body {
            padding: 20px;
        }
        .order-items {
            margin-bottom: 20px;
        }
        .order-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        .order-item:last-child {
            border-bottom: none;
        }
        .item-image {
            width: 60px;
            height: 60px;
            border-radius: 8px;
            object-fit: cover;
        }
        .item-details {
            flex: 1;
        }
        .item-name {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .item-desc {
            color: #6c757d;
            font-size: 0.9rem;
        }
        .item-price {
            font-weight: bold;
            color: var(--steam-green);
        }
        .order-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: 20px;
            border-top: 1px solid #f0f0f0;
        }
        .order-total {
            font-size: 1.2rem;
            font-weight: bold;
            color: #2c3e50;
        }
        .order-actions {
            display: flex;
            gap: 10px;
        }
        .btn-sm {
            padding: 8px 16px;
            font-size: 0.9rem;
        }
        .delivery-info {
            background: linear-gradient(45deg, rgba(144, 186, 60, 0.1), rgba(144, 186, 60, 0.05));
            border: 1px solid rgba(144, 186, 60, 0.3);
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
        }
        .delivery-title {
            font-weight: bold;
            color: var(--steam-green);
            margin-bottom: 10px;
        }
        .delivery-code {
            font-family: monospace;
            background: white;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #ddd;
            margin-bottom: 10px;
        }
        .copy-btn {
            background: var(--steam-green);
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.8rem;
        }
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }
        .empty-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.5;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="container">
            <div class="nav-brand">
                <h1>SteamPY</h1>
                <span class="nav-subtitle">Steam游戏交易平台</span>
            </div>
            
            <div class="nav-menu">
                <a href="index" class="nav-link">首页</a>
                <a href="products" class="nav-link">产品商城</a>
                <a href="wallet" class="nav-link">Steam钱包</a>
                <a href="orders" class="nav-link active">我的订单</a>
                <a href="support" class="nav-link">客服支持</a>
            </div>
            
            <div class="nav-actions">
                <a href="profile" class="nav-link">个人中心</a>
                <a href="login" class="btn btn-primary">登录</a>
                <a href="register" class="btn btn-outline">注册</a>
            </div>
        </div>
    </nav>

    <!-- 页面标题 -->
    <section class="page-header">
        <div class="container">
            <h1 class="page-title">📦 我的订单</h1>
            <p class="page-subtitle">管理您的购买订单，查看交易记录和配送状态</p>
        </div>
    </section>

    <!-- 订单管理 -->
    <section class="orders-section">
        <div class="container">
            <!-- 订单统计 -->
            <div class="orders-stats">
                <div class="stat-card">
                    <div class="stat-number">12</div>
                    <div class="stat-label">总订单数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">3</div>
                    <div class="stat-label">进行中</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">8</div>
                    <div class="stat-label">已完成</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">¥1,280</div>
                    <div class="stat-label">累计消费</div>
                </div>
            </div>

            <!-- 订单筛选 -->
            <div class="orders-filter">
                <div class="filter-tabs">
                    <button class="filter-tab active" data-status="all">全部订单</button>
                    <button class="filter-tab" data-status="pending">待处理</button>
                    <button class="filter-tab" data-status="processing">处理中</button>
                    <button class="filter-tab" data-status="completed">已完成</button>
                    <button class="filter-tab" data-status="failed">已取消</button>
                </div>
                
                <div class="filter-row">
                    <div class="filter-group">
                        <label>时间范围：</label>
                        <select id="time-filter">
                            <option value="">全部时间</option>
                            <option value="7">最近7天</option>
                            <option value="30">最近30天</option>
                            <option value="90">最近3个月</option>
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <label>订单类型：</label>
                        <select id="type-filter">
                            <option value="">全部类型</option>
                            <option value="wallet">Steam钱包</option>
                            <option value="game">游戏激活码</option>
                            <option value="item">游戏道具</option>
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <label>搜索订单：</label>
                        <input type="text" id="search-input" placeholder="输入订单号或商品名称">
                    </div>
                </div>
            </div>

            <!-- 订单列表 -->
            <div class="orders-list" id="orders-list">
                <!-- 订单1 - 已完成 -->
                <div class="order-card" data-status="completed">
                    <div class="order-header">
                        <div class="order-info">
                            <div class="order-id">订单号：SP20241224001</div>
                            <div class="order-date">2024-12-24 14:30</div>
                        </div>
                        <div class="order-status status-completed">已完成</div>
                    </div>
                    <div class="order-body">
                        <div class="order-items">
                            <div class="order-item">
                                <img src="https://images.unsplash.com/photo-1511512578047-dfb367046420?w=60&h=60&fit=crop" alt="Steam钱包" class="item-image">
                                <div class="item-details">
                                    <div class="item-name">Steam钱包充值码 ¥100</div>
                                    <div class="item-desc">数字充值码，即时到账</div>
                                </div>
                                <div class="item-price">¥95.00</div>
                            </div>
                        </div>
                        
                        <div class="delivery-info">
                            <div class="delivery-title">🎁 配送信息</div>
                            <div>充值码：<span class="delivery-code">STEAM-ABCD-1234-EFGH</span> 
                                <button class="copy-btn" onclick="copyToClipboard('STEAM-ABCD-1234-EFGH')">复制</button>
                            </div>
                            <div style="margin-top: 8px; font-size: 0.9rem; color: #6c757d;">
                                请在Steam客户端或官网兑换此充值码
                            </div>
                        </div>
                        
                        <div class="order-footer">
                            <div class="order-total">总计：¥95.00</div>
                            <div class="order-actions">
                                <button class="btn btn-outline btn-sm">再次购买</button>
                                <button class="btn btn-primary btn-sm">评价订单</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 订单2 - 处理中 -->
                <div class="order-card" data-status="processing">
                    <div class="order-header">
                        <div class="order-info">
                            <div class="order-id">订单号：SP20241224002</div>
                            <div class="order-date">2024-12-24 16:15</div>
                        </div>
                        <div class="order-status status-processing">处理中</div>
                    </div>
                    <div class="order-body">
                        <div class="order-items">
                            <div class="order-item">
                                <img src="https://images.unsplash.com/photo-1542751371-adc38448a05e?w=60&h=60&fit=crop" alt="Cyberpunk 2077" class="item-image">
                                <div class="item-details">
                                    <div class="item-name">Cyberpunk 2077</div>
                                    <div class="item-desc">Steam激活码</div>
                                </div>
                                <div class="item-price">¥198.00</div>
                            </div>
                        </div>
                        
                        <div class="order-footer">
                            <div class="order-total">总计：¥198.00</div>
                            <div class="order-actions">
                                <button class="btn btn-outline btn-sm">联系客服</button>
                                <button class="btn btn-primary btn-sm">查看详情</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 订单3 - 待处理 -->
                <div class="order-card" data-status="pending">
                    <div class="order-header">
                        <div class="order-info">
                            <div class="order-id">订单号：SP20241224003</div>
                            <div class="order-date">2024-12-24 18:45</div>
                        </div>
                        <div class="order-status status-pending">待处理</div>
                    </div>
                    <div class="order-body">
                        <div class="order-items">
                            <div class="order-item">
                                <img src="https://images.unsplash.com/photo-1519389950473-47ba0277781c?w=60&h=60&fit=crop" alt="CS2皮肤" class="item-image">
                                <div class="item-details">
                                    <div class="item-name">CS2 AK-47 红线皮肤</div>
                                    <div class="item-desc">游戏道具</div>
                                </div>
                                <div class="item-price">¥299.00</div>
                            </div>
                        </div>
                        
                        <div class="order-footer">
                            <div class="order-total">总计：¥299.00</div>
                            <div class="order-actions">
                                <button class="btn btn-outline btn-sm">取消订单</button>
                                <button class="btn btn-primary btn-sm">立即支付</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 空状态 -->
            <div class="empty-state" id="empty-state" style="display: none;">
                <div class="empty-icon">📦</div>
                <h3>暂无订单</h3>
                <p>您还没有任何订单，去商城看看吧！</p>
                <a href="products.html" class="btn btn-primary">前往商城</a>
            </div>
        </div>
    </section>

    <!-- 页脚 -->
    <footer class="footer">
        <div class="container">
            <p>&copy; 2024 SteamPY. All rights reserved.</p>
        </div>
    </footer>

    <script src="/scripts/main.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            
            // 筛选功能
            const filterTabs = document.querySelectorAll('.filter-tab');
            const orderCards = document.querySelectorAll('.order-card');
            const timeFilter = document.getElementById('time-filter');
            const typeFilter = document.getElementById('type-filter');
            const searchInput = document.getElementById('search-input');
            
            // 状态筛选
            filterTabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    filterTabs.forEach(t => t.classList.remove('active'));
                    this.classList.add('active');
                    
                    const status = this.dataset.status;
                    filterOrders();
                });
            });
            
            // 搜索和其他筛选
            timeFilter.addEventListener('change', filterOrders);
            typeFilter.addEventListener('change', filterOrders);
            searchInput.addEventListener('input', filterOrders);
            
            function filterOrders() {
                const activeStatus = document.querySelector('.filter-tab.active').dataset.status;
                const searchTerm = searchInput.value.toLowerCase();
                
                let visibleCount = 0;
                
                orderCards.forEach(card => {
                    const cardStatus = card.dataset.status;
                    const orderText = card.textContent.toLowerCase();
                    
                    let isVisible = true;
                    
                    // 状态筛选
                    if (activeStatus !== 'all' && cardStatus !== activeStatus) {
                        isVisible = false;
                    }
                    
                    // 搜索筛选
                    if (searchTerm && !orderText.includes(searchTerm)) {
                        isVisible = false;
                    }
                    
                    card.style.display = isVisible ? 'block' : 'none';
                    if (isVisible) visibleCount++;
                });
                
                // 显示/隐藏空状态
                const emptyState = document.getElementById('empty-state');
                const ordersList = document.getElementById('orders-list');
                
                if (visibleCount === 0) {
                    ordersList.style.display = 'none';
                    emptyState.style.display = 'block';
                } else {
                    ordersList.style.display = 'flex';
                    emptyState.style.display = 'none';
                }
            }
            
            // 订单操作
            document.addEventListener('click', function(e) {
                if (e.target.classList.contains('copy-btn')) {
                    // 处理复制按钮点击已在 onclick 中处理
                } else if (e.target.textContent === '再次购买') {
                    showMessage('已添加到购物车', 'success');
                } else if (e.target.textContent === '评价订单') {
                    showMessage('评价功能开发中...', 'info');
                } else if (e.target.textContent === '联系客服') {
                    window.location.href = 'support.html';
                } else if (e.target.textContent === '查看详情') {
                    showMessage('订单详情功能开发中...', 'info');
                } else if (e.target.textContent === '取消订单') {
                    if (confirm('确定要取消这个订单吗？')) {
                        showMessage('订单已取消', 'success');
                        e.target.closest('.order-card').remove();
                    }
                } else if (e.target.textContent === '立即支付') {
                    showMessage('正在跳转到支付页面...', 'info');
                }
            });
            
            // 检查登录状态
            if (!authManager || !authManager.isLoggedIn) {
                document.querySelector('.orders-section').innerHTML = `
                    <div class="container">
                        <div class="empty-state">
                            <div class="empty-icon">🔒</div>
                            <h3>请先登录</h3>
                            <p>登录后即可查看您的订单记录</p>
                            <a href="login.html" class="btn btn-primary">立即登录</a>
                        </div>
                    </div>
                `;
            }
        });
        
        // 复制到剪贴板
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                showMessage('充值码已复制到剪贴板', 'success');
            }).catch(() => {
                showMessage('复制失败，请手动复制', 'error');
            });
        }
    </script>
</body>
</html> 
