#!/bin/bash

echo "🚀 启动 SteamPY 项目 (本地开发模式)..."

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 未安装，请先安装 Python 3.11+"
    exit 1
fi

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装，请先安装 Node.js 18+"
    exit 1
fi

echo "📦 安装后端依赖..."
cd backend
if [ ! -d "venv" ]; then
    echo "创建Python虚拟环境..."
    python3 -m venv venv
fi

# 激活虚拟环境
source venv/bin/activate || source venv/Scripts/activate

# 安装依赖
pip install -r requirements.txt

echo "🚀 启动后端服务..."
python main.py &
BACKEND_PID=$!

cd ../frontend

echo "📦 安装前端依赖..."
npm install

echo "🚀 启动前端服务..."
npm run dev &
FRONTEND_PID=$!

echo ""
echo "✅ 项目启动完成！"
echo "🌐 前端地址: http://localhost:3000"
echo "🔗 后端API: http://localhost:8000"
echo "📚 API文档: http://localhost:8000/docs"
echo "🗄️  数据库: SQLite (backend/steampy.db)"
echo ""
echo "📋 默认管理员账户:"
echo "   用户名: admin"
echo "   密码: admin123"
echo ""
echo "⏹️  停止服务: 按 Ctrl+C"

# 等待用户按Ctrl+C
trap "echo '🛑 正在停止服务...'; kill $BACKEND_PID $FRONTEND_PID; exit" INT
wait 