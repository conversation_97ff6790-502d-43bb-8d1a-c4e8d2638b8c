"""
Rate limiting and security middleware for the application.
"""
import time
from collections import defaultdict, deque
from typing import Dict, Deque
from fastapi import Request, Response, HTTPException
from fastapi.responses import J<PERSON>NResponse
from app.core.config import settings


# 存储每个IP的请求时间戳
request_history: Dict[str, Deque[float]] = defaultdict(lambda: deque())


async def rate_limit_middleware(request: Request, call_next):
    """
    Rate limiting middleware based on client IP address.
    
    Tracks requests per IP and enforces rate limits based on configuration.
    """
    # 获取客户端IP地址
    client_ip = request.client.host
    if not client_ip:
        client_ip = "unknown"
    
    # 获取当前时间
    current_time = time.time()
    
    # 获取该IP的请求历史
    ip_history = request_history[client_ip]
    
    # 清理过期的请求记录（超出时间窗口的）
    cutoff_time = current_time - settings.RATE_LIMIT_WINDOW
    while ip_history and ip_history[0] <= cutoff_time:
        ip_history.popleft()
    
    # 检查是否超过速率限制
    if len(ip_history) >= settings.RATE_LIMIT_REQUESTS:
        # 返回429状态码（Too Many Requests）
        return JSONResponse(
            status_code=429,
            content={
                "success": False,
                "message": "Rate limit exceeded. Please try again later.",
                "error_code": 429,
                "retry_after": settings.RATE_LIMIT_WINDOW
            },
            headers={
                "Retry-After": str(settings.RATE_LIMIT_WINDOW),
                "X-RateLimit-Limit": str(settings.RATE_LIMIT_REQUESTS),
                "X-RateLimit-Remaining": "0",
                "X-RateLimit-Reset": str(int(current_time + settings.RATE_LIMIT_WINDOW))
            }
        )
    
    # 记录当前请求
    ip_history.append(current_time)
    
    # 处理请求
    response = await call_next(request)
    
    # 添加速率限制相关的响应头
    remaining_requests = settings.RATE_LIMIT_REQUESTS - len(ip_history)
    response.headers["X-RateLimit-Limit"] = str(settings.RATE_LIMIT_REQUESTS)
    response.headers["X-RateLimit-Remaining"] = str(remaining_requests)
    response.headers["X-RateLimit-Reset"] = str(int(current_time + settings.RATE_LIMIT_WINDOW))
    
    return response


def cleanup_old_records():
    """
    清理过期的请求记录，防止内存泄漏。
    这个函数可以定期调用来清理过期数据。
    """
    current_time = time.time()
    cutoff_time = current_time - settings.RATE_LIMIT_WINDOW
    
    # 清理所有IP的过期记录
    ips_to_remove = []
    for ip, history in request_history.items():
        # 移除过期的记录
        while history and history[0] <= cutoff_time:
            history.popleft()
        
        # 如果该IP没有任何记录，标记为删除
        if not history:
            ips_to_remove.append(ip)
    
    # 删除空的IP记录
    for ip in ips_to_remove:
        del request_history[ip]


# 可选：IP白名单功能
WHITELIST_IPS = {
    "127.0.0.1",
    "localhost",
    "::1"
}


async def rate_limit_middleware_with_whitelist(request: Request, call_next):
    """
    带白名单功能的速率限制中间件。
    白名单中的IP不受速率限制。
    """
    # 获取客户端IP地址
    client_ip = request.client.host
    if not client_ip:
        client_ip = "unknown"
    
    # 检查是否在白名单中
    if client_ip in WHITELIST_IPS:
        return await call_next(request)
    
    # 对非白名单IP应用速率限制
    return await rate_limit_middleware(request, call_next) 