import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import ClientBody from "./ClientBody";

const inter = Inter({
  subsets: ["latin"],
  display: "swap",
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "AI Poster Generator | Create Stunning Posters with AI",
  description: "Transform your ideas into eye-catching posters with our AI Poster Generator. No design skills required. Try our AI Poster Generator and unleash your creativity!",
  icons: {
    icon: [
      { url: "/logo.png", sizes: "32x32" },
    ],
  },
  metadataBase: new URL("https://deeposter-clone.netlify.app"),
  openGraph: {
    title: "DeePoster - AI Poster Generator",
    description: "Transform your ideas into eye-catching posters with our AI Poster Generator. No design skills required.",
    images: ["/logo.png"],
  },
  twitter: {
    card: "summary_large_image",
    title: "DeePoster - AI Poster Generator",
    description: "Transform your ideas into eye-catching posters with our AI Poster Generator. No design skills required.",
    images: ["/logo.png"],
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={inter.variable}>
      <body className="min-h-screen antialiased">
        <ClientBody>{children}</ClientBody>
      </body>
    </html>
  );
}
