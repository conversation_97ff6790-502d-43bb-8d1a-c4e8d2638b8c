import { Button } from "@/components/ui/button";

export function CTA() {
  return (
    <section className="py-24 relative grid-background">
      <div className="absolute inset-0 bg-gradient-to-tr from-primary/30 via-transparent to-transparent opacity-30" />
      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-3xl mx-auto text-center">
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            Ready to create amazing posters?
          </h2>
          <p className="text-xl text-muted-foreground mb-10">
            Join thousands of creators who are already using <PERSON><PERSON><PERSON><PERSON> to turn their ideas into stunning visuals.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="rounded-full px-8 py-6 h-auto text-lg">
              <span className="mr-2">✨</span> Start for free
            </Button>
            <Button variant="outline" size="lg" className="rounded-full px-8 py-6 h-auto text-lg">
              View Gallery
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}
