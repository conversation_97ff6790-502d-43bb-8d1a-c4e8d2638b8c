# 故障排除指南

## 🛠️ 常见问题及解决方案

### 前端问题

#### 1. Next.js 配置错误：Package subpath './plugin' is not defined

**错误信息：**
```
Error [ERR_PACKAGE_PATH_NOT_EXPORTED]: Package subpath './plugin' is not defined by "exports" in next-intl/package.json
```

**原因：** `next-intl` 包与 Node.js 16 不完全兼容

**解决方案：**
```bash
cd frontend
npm uninstall next-intl
# 然后更新 next.config.js，移除 next-intl 相关配置
```

#### 2. React Hook Form 引擎警告

**错误信息：**
```
npm WARN EBADENGINE Unsupported engine { package: 'react-hook-form@7.58.1', required: { node: '>=18.0.0' } }
```

**解决方案：** 
项目已降级到兼容版本，警告可以忽略，或者升级 Node.js 到 18+

#### 3. 前端启动失败

**错误：BUILD_ID 文件不存在**
```
Error: ENOENT: no such file or directory, open '.next\BUILD_ID'
```

**原因：** 使用 `npm start` 之前没有先构建项目

**解决方案：**
```bash
# 开发模式（推荐）
npm run dev

# 或者生产模式
npm run build
npm start
```

**其他检查步骤：**
1. 确认 Node.js 版本：`node --version`
2. 清理缓存：`npm cache clean --force`
3. 重新安装依赖：`npm install`
4. 检查端口占用：`netstat -ano | findstr :3000`

### 后端问题

#### 1. 数据库连接错误

**错误信息：**
```
(sqlite3.OperationalError) no such table: users
```

**解决方案：**
1. 删除现有数据库文件：`rm backend/steampy.db`
2. 重新启动后端，自动创建表

#### 2. Python 虚拟环境问题

**Windows:**
```cmd
cd backend
python -m venv venv
venv\Scripts\activate
pip install -r requirements.txt
```

**Linux/Mac:**
```bash
cd backend
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt
```

#### 3. 端口占用

**检查端口 8000：**
```bash
# Windows
netstat -ano | findstr :8000

# Linux/Mac
lsof -i :8000
```

**杀死进程：**
```bash
# Windows
taskkill /PID <PID> /F

# Linux/Mac
kill -9 <PID>
```

### Docker 问题

#### 1. Docker 服务未启动

**Windows:** 确保 Docker Desktop 正在运行
**Linux:** `sudo systemctl start docker`

#### 2. 构建失败

```bash
# 清理 Docker 缓存
docker system prune -a

# 重新构建
docker-compose build --no-cache
docker-compose up
```

### 网络问题

#### 1. API 连接失败

**检查后端状态：**
- 浏览器访问：http://localhost:8000/docs
- 健康检查：http://localhost:8000/health

**检查防火墙设置：**
确保端口 3000 和 8000 未被防火墙阻止

#### 2. CORS 错误

确保后端配置中包含前端域名：
```python
CORS_ORIGINS = ["http://localhost:3000"]
```

## 🔧 调试工具

### 1. 查看日志

**前端日志：**
- 浏览器开发者工具 Console 选项卡
- 终端中的 Next.js 输出

**后端日志：**
- FastAPI 自动日志输出
- 可添加自定义日志记录

### 2. 网络调试

**浏览器 Network 选项卡：**
- 检查 API 请求状态
- 查看请求/响应头
- 确认数据格式

### 3. 数据库调试

**SQLite 浏览器：**
- 安装 DB Browser for SQLite
- 打开 `backend/steampy.db` 文件
- 检查表结构和数据

## 🚨 重置项目

如果遇到无法解决的问题，可以重置项目：

### 1. 清理前端
```bash
cd frontend
rm -rf node_modules package-lock.json .next
npm install
```

### 2. 清理后端
```bash
cd backend
rm -rf venv __pycache__ steampy.db
python -m venv venv
# Windows: venv\Scripts\activate
# Linux/Mac: source venv/bin/activate
pip install -r requirements.txt
```

### 3. 完全重置
```bash
# 删除所有生成的文件
rm -rf frontend/node_modules frontend/.next
rm -rf backend/venv backend/__pycache__ backend/steampy.db

# 重新启动项目
./start-windows.bat  # Windows
./start.sh           # Linux/Mac
```

## 📞 获取帮助

如果问题仍然存在：

1. **检查 README.md** - 确保满足所有环境要求
2. **查看 GIT_GUIDE.md** - 了解版本控制最佳实践
3. **检查项目 Issues** - 看是否有类似问题
4. **创建新 Issue** - 提供详细的错误信息和环境配置

### 报告问题时请提供：
- 操作系统版本
- Node.js 版本：`node --version`
- Python 版本：`python --version`
- 完整的错误信息
- 重现步骤

## ✅ 验证修复

完成修复后，验证项目是否正常工作：

1. **前端验证：**
   - 访问 http://localhost:3000
   - 检查页面是否正常显示

2. **后端验证：**
   - 访问 http://localhost:8000/docs
   - 测试 API 端点

3. **功能验证：**
   - 尝试用户注册/登录
   - 检查数据库是否正常创建

如果所有检查都通过，说明项目已成功修复！ 