# Git 使用指南

## 📋 项目文件管理

### ✅ 应该提交到Git的文件
- 源代码文件 (`.py`, `.ts`, `.tsx`, `.js`, `.jsx`)
- 配置文件 (`package.json`, `requirements.txt`, `tsconfig.json`, 等)
- 文档文件 (`README.md`, `*.md`)
- 静态资源 (图片、字体、CSS等)
- 示例配置文件 (`.env.example`)
- 构建脚本和启动脚本 (`start-*.sh`, `*.bat`)
- Docker 配置 (`Dockerfile`, `docker-compose.yml`)

### ❌ 不应该提交到Git的文件（已在.gitignore中）
- 依赖包目录 (`node_modules/`, `venv/`)
- 构建输出 (`.next/`, `dist/`, `build/`)
- 数据库文件 (`*.db`, `*.sqlite`)
- 环境配置文件 (`.env`)
- 日志文件 (`*.log`)
- 缓存文件 (`__pycache__/`, `.cache/`)
- IDE配置 (`.idea/`, `.vscode/`)
- 操作系统文件 (`.DS_Store`, `Thumbs.db`)

## 🚀 Git 基本使用流程

### 1. 初始化仓库（如果还未初始化）
```bash
git init
git add .
git commit -m "Initial commit: SteamPY project setup"
```

### 2. 添加远程仓库
```bash
git remote add origin https://github.com/your-username/steampy.git
git branch -M main
git push -u origin main
```

### 3. 日常开发流程
```bash
# 查看文件状态
git status

# 添加修改的文件
git add .
# 或添加特定文件
git add path/to/file.py

# 提交更改
git commit -m "Add new feature: user authentication"

# 推送到远程仓库
git push origin main
```

### 4. 分支管理
```bash
# 创建新分支
git checkout -b feature/new-feature

# 切换分支
git checkout main

# 合并分支
git merge feature/new-feature

# 删除分支
git branch -d feature/new-feature
```

## 📝 提交信息规范

使用有意义的提交信息：

```bash
# ✅ 好的提交信息
git commit -m "Add user registration API endpoint"
git commit -m "Fix database connection error"
git commit -m "Update frontend login component"
git commit -m "Improve error handling in auth module"

# ❌ 不好的提交信息
git commit -m "fix"
git commit -m "update"
git commit -m "changes"
```

### 提交信息格式建议
```
<type>: <description>

类型(type)可以是:
- feat: 新功能
- fix: 修复bug
- docs: 文档更新
- style: 代码格式化（不影响功能）
- refactor: 代码重构
- test: 添加或修改测试
- chore: 构建过程或辅助工具的变动
```

例如：
```bash
git commit -m "feat: add Steam balance trading API"
git commit -m "fix: resolve database connection timeout"
git commit -m "docs: update installation guide"
```

## 🔧 常用Git命令

### 查看状态和历史
```bash
git status              # 查看文件状态
git log --oneline       # 查看提交历史
git diff                # 查看未暂存的更改
git diff --staged       # 查看已暂存的更改
```

### 撤销操作
```bash
git checkout -- file.py    # 撤销文件的修改
git reset HEAD file.py      # 取消暂存文件
git reset --soft HEAD~1     # 撤销上一次提交（保留更改）
git reset --hard HEAD~1     # 撤销上一次提交（丢弃更改）
```

### 远程仓库操作
```bash
git remote -v           # 查看远程仓库
git fetch origin        # 获取远程更新
git pull origin main    # 拉取并合并远程更新
git push origin main    # 推送到远程仓库
```

## 🛡️ 最佳实践

1. **频繁提交**：经常提交小的更改，便于追踪和回滚
2. **清晰的提交信息**：使用描述性的提交信息
3. **使用分支**：为不同功能使用不同分支
4. **定期同步**：定期从远程仓库拉取更新
5. **测试后提交**：确保代码能正常运行后再提交

## 🚨 注意事项

1. **永远不要提交敏感信息**：
   - 密码、API密钥
   - 数据库连接字符串
   - 个人配置文件

2. **检查.gitignore**：
   - 确保不必要的文件被忽略
   - 定期更新.gitignore规则

3. **备份重要数据**：
   - 数据库文件
   - 配置文件
   - 用户上传的文件

## 📊 当前项目状态

当前项目已配置了完整的.gitignore文件，包含：
- Python缓存文件
- Node.js依赖和构建文件
- 数据库文件
- 环境配置文件
- IDE配置文件
- 操作系统临时文件

您可以安全地使用以下命令开始版本控制：
```bash
git add .
git commit -m "Initial commit: SteamPY project with Node.js 16 compatibility"
``` 