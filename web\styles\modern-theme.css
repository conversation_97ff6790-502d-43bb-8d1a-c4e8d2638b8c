/**
 * 现代化主题样式 - 参考现代UI设计趋势
 * 灵感来源：Material Design 3, Apple Human Interface Guidelines, Fluent Design
 */

/* ==================== 设计系统变量 ==================== */
:root {
    /* 主题色彩 - 更现代的配色方案 */
    --primary-50: #f0f9ff;
    --primary-100: #e0f2fe;
    --primary-200: #bae6fd;
    --primary-300: #7dd3fc;
    --primary-400: #38bdf8;
    --primary-500: #0ea5e9;
    --primary-600: #0284c7;
    --primary-700: #0369a1;
    --primary-800: #075985;
    --primary-900: #0c4a6e;

    /* Steam主题色彩 - 保持品牌一致性 */
    --steam-blue: #1a202c;
    --steam-blue-light: #2d3748;
    --steam-blue-dark: #171923;
    --steam-green: #48bb78;
    --steam-green-light: #68d391;
    --steam-green-dark: #38a169;
    --steam-accent: #ed8936;

    /* 中性色系 - 更柔和的灰色 */
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;

    /* 语义色彩 */
    --success: #10b981;
    --warning: #f59e0b;
    --error: #ef4444;
    --info: #3b82f6;

    /* 字体系统 */
    --font-sans: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    --font-mono: ui-monospace, SFMono-Regular, "SF Mono", Monaco, Consolas, monospace;

    /* 阴影系统 */
    --shadow-xs: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-sm: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);

    /* 边框半径 */
    --radius-none: 0;
    --radius-sm: 0.125rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;
    --radius-3xl: 1.5rem;
    --radius-full: 9999px;

    /* 间距系统 */
    --space-px: 1px;
    --space-0: 0;
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    --space-16: 4rem;
    --space-20: 5rem;
    --space-24: 6rem;
    --space-32: 8rem;

    /* 过渡动画 */
    --transition-all: all 0.15s ease-in-out;
    --transition-colors: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out;
    --transition-transform: transform 0.15s ease-in-out;
    --transition-opacity: opacity 0.15s ease-in-out;
}

/* ==================== 重置样式 ==================== */
*,
*::before,
*::after {
    box-sizing: border-box;
}

* {
    margin: 0;
    padding: 0;
}

html {
    line-height: 1.15;
    -webkit-text-size-adjust: 100%;
    tab-size: 4;
}

body {
    font-family: var(--font-sans);
    line-height: 1.6;
    font-weight: 400;
    color: var(--gray-900);
    background-color: var(--gray-50);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* ==================== 现代化组件系统 ==================== */

/* 按钮组件 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
    padding: var(--space-3) var(--space-6);
    font-size: 0.875rem;
    font-weight: 500;
    line-height: 1.25rem;
    text-decoration: none;
    border: 1px solid transparent;
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: var(--transition-all);
    white-space: nowrap;
    outline: none;
    user-select: none;
}

.btn:focus-visible {
    outline: 2px solid var(--primary-500);
    outline-offset: 2px;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* 按钮变体 */
.btn-primary {
    background: linear-gradient(135deg, var(--steam-green) 0%, var(--steam-green-dark) 100%);
    color: white;
    box-shadow: var(--shadow-sm);
}

.btn-primary:hover:not(:disabled) {
    background: linear-gradient(135deg, var(--steam-green-dark) 0%, var(--steam-green) 100%);
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
}

.btn-secondary {
    background: var(--gray-100);
    color: var(--gray-900);
    border-color: var(--gray-300);
}

.btn-secondary:hover:not(:disabled) {
    background: var(--gray-200);
    border-color: var(--gray-400);
}

.btn-outline {
    background: transparent;
    color: var(--steam-green);
    border-color: var(--steam-green);
}

.btn-outline:hover:not(:disabled) {
    background: var(--steam-green);
    color: white;
    box-shadow: var(--shadow-sm);
}

.btn-ghost {
    background: transparent;
    color: var(--gray-600);
}

.btn-ghost:hover:not(:disabled) {
    background: var(--gray-100);
    color: var(--gray-900);
}

/* 按钮尺寸 */
.btn-sm {
    padding: var(--space-2) var(--space-4);
    font-size: 0.75rem;
}

.btn-lg {
    padding: var(--space-4) var(--space-8);
    font-size: 1rem;
}

.btn-xl {
    padding: var(--space-5) var(--space-10);
    font-size: 1.125rem;
}

/* 卡片组件 */
.card {
    background: white;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
    overflow: hidden;
    transition: var(--transition-all);
}

.card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.card-header {
    padding: var(--space-6);
    border-bottom: 1px solid var(--gray-200);
    background: var(--gray-50);
}

.card-body {
    padding: var(--space-6);
}

.card-footer {
    padding: var(--space-6);
    border-top: 1px solid var(--gray-200);
    background: var(--gray-50);
}

/* 输入框组件 */
.input {
    width: 100%;
    padding: var(--space-3) var(--space-4);
    font-size: 0.875rem;
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-lg);
    background: white;
    transition: var(--transition-colors);
    outline: none;
}

.input:focus {
    border-color: var(--steam-green);
    box-shadow: 0 0 0 3px rgb(72 187 120 / 0.1);
}

.input:disabled {
    background: var(--gray-100);
    color: var(--gray-500);
    cursor: not-allowed;
}

/* 现代化导航栏 */
.navbar-modern {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(12px);
    border-bottom: 1px solid var(--gray-200);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.navbar-modern .container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--space-4) var(--space-6);
    max-width: 1200px;
    margin: 0 auto;
}

.nav-brand-modern {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    text-decoration: none;
    color: var(--gray-900);
}

.nav-brand-modern h1 {
    font-size: 1.5rem;
    font-weight: 700;
    background: linear-gradient(135deg, var(--steam-blue) 0%, var(--steam-green) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.nav-menu-modern {
    display: flex;
    align-items: center;
    gap: var(--space-8);
}

.nav-link-modern {
    color: var(--gray-600);
    text-decoration: none;
    font-weight: 500;
    padding: var(--space-2) var(--space-4);
    border-radius: var(--radius-lg);
    transition: var(--transition-colors);
    position: relative;
}

.nav-link-modern:hover,
.nav-link-modern.active {
    color: var(--steam-green);
    background: rgba(72, 187, 120, 0.1);
}

.nav-link-modern.active::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 24px;
    height: 2px;
    background: var(--steam-green);
    border-radius: var(--radius-full);
}

/* 现代化产品卡片 */
.product-card-modern {
    background: white;
    border-radius: var(--radius-2xl);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: var(--transition-all);
    border: 1px solid var(--gray-200);
}

.product-card-modern:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

.product-image-modern {
    position: relative;
    aspect-ratio: 16/9;
    overflow: hidden;
}

.product-image-modern img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition-transform);
}

.product-card-modern:hover .product-image-modern img {
    transform: scale(1.05);
}

.product-badge-modern {
    position: absolute;
    top: var(--space-3);
    left: var(--space-3);
    background: linear-gradient(135deg, var(--error) 0%, #dc2626 100%);
    color: white;
    padding: var(--space-1) var(--space-3);
    border-radius: var(--radius-full);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.product-info-modern {
    padding: var(--space-6);
}

.product-title-modern {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--space-2);
    line-height: 1.4;
}

.product-description-modern {
    color: var(--gray-600);
    font-size: 0.875rem;
    margin-bottom: var(--space-4);
    line-height: 1.5;
}

.product-price-modern {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    margin-bottom: var(--space-4);
}

.price-current-modern {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--steam-green);
}

.price-original-modern {
    font-size: 0.875rem;
    color: var(--gray-500);
    text-decoration: line-through;
}

/* 现代化英雄区域 */
.hero-modern {
    background: linear-gradient(135deg, var(--steam-blue) 0%, var(--steam-blue-light) 100%);
    color: white;
    padding: var(--space-24) 0;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.hero-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 50% 50%, rgba(72, 187, 120, 0.1) 0%, transparent 50%);
}

.hero-content-modern {
    position: relative;
    z-index: 1;
    max-width: 800px;
    margin: 0 auto;
    padding: 0 var(--space-6);
}

.hero-title-modern {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: var(--space-6);
    background: linear-gradient(135deg, white 0%, var(--steam-green-light) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle-modern {
    font-size: 1.25rem;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: var(--space-8);
    line-height: 1.6;
}

/* 现代化特性卡片 */
.feature-card-modern {
    background: white;
    border-radius: var(--radius-2xl);
    padding: var(--space-8);
    text-align: center;
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--gray-200);
    transition: var(--transition-all);
}

.feature-card-modern:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-2xl);
}

.feature-icon-modern {
    width: 64px;
    height: 64px;
    background: linear-gradient(135deg, var(--steam-green) 0%, var(--steam-green-light) 100%);
    border-radius: var(--radius-2xl);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin: 0 auto var(--space-6);
    color: white;
}

/* 现代化状态指示器 */
.status-modern {
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-1) var(--space-3);
    border-radius: var(--radius-full);
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.status-success-modern {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success);
}

.status-warning-modern {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning);
}

.status-error-modern {
    background: rgba(239, 68, 68, 0.1);
    color: var(--error);
}

.status-info-modern {
    background: rgba(59, 130, 246, 0.1);
    color: var(--info);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .nav-menu-modern {
        display: none;
    }
    
    .hero-modern {
        padding: var(--space-16) 0;
    }
    
    .feature-card-modern {
        padding: var(--space-6);
    }
    
    .product-info-modern {
        padding: var(--space-4);
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    :root {
        --gray-50: #111827;
        --gray-100: #1f2937;
        --gray-200: #374151;
        --gray-300: #4b5563;
        --gray-400: #6b7280;
        --gray-500: #9ca3af;
        --gray-600: #d1d5db;
        --gray-700: #e5e7eb;
        --gray-800: #f3f4f6;
        --gray-900: #f9fafb;
    }
    
    body {
        background-color: var(--gray-50);
        color: var(--gray-900);
    }
    
    .card {
        background: var(--gray-100);
        border-color: var(--gray-200);
    }
    
    .navbar-modern {
        background: rgba(17, 24, 39, 0.8);
        border-bottom-color: var(--gray-200);
    }
} 