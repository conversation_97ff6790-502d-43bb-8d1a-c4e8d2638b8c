import Image from "next/image";
import { PosterInputPlaceholder, PosterResultPlaceholder, DownloadButtonPlaceholder } from "./ui-placeholders";

export function Features() {
  return (
    <section className="py-20 bg-background" id="features">
      <div className="container mx-auto px-4">
        <h2 className="text-4xl font-bold text-center mb-16">
          Get started with a few easy steps
        </h2>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <FeatureCard
            number={1}
            title="Type a Prompt"
            description="Input your desired poster title and content. The AI will automatically generate a custom design based on your input."
            component={<PosterInputPlaceholder />}
          />
          <FeatureCard
            number={2}
            title="Press Generate"
            description="Our advanced AI technology creates multiple poster designs for you to choose from in seconds."
            component={<PosterResultPlaceholder />}
          />
          <FeatureCard
            number={3}
            title="Download"
            description="Download the poster to your local disk. Your poster looks amazing!"
            component={<DownloadButtonPlaceholder />}
          />
        </div>

        <div className="mt-32">
          <h2 className="text-4xl font-bold text-center mb-16">
            Why choose DeePoster
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-20">
            <div className="flex flex-col justify-center">
              <h3 className="text-2xl font-bold mb-4">Beautiful AI Generated Posters</h3>
              <p className="text-muted-foreground mb-4">
                In a world of visual uniqueness, DeePoster's AI offers diverse poster styles.
                No design skills needed. Choose a style and hit "Create". Our AI turns your
                ideas into eye-catching posters.
              </p>
              <ul className="space-y-2">
                <li className="flex items-center gap-2">
                  <span className="text-primary">✓</span> Multiple poster styles and themes
                </li>
                <li className="flex items-center gap-2">
                  <span className="text-primary">✓</span> High resolution outputs
                </li>
                <li className="flex items-center gap-2">
                  <span className="text-primary">✓</span> Commercial usage allowed
                </li>
              </ul>
            </div>
            <div className="flex items-center justify-center">
              <div className="bg-gradient-to-tr from-indigo-600 to-violet-500 rounded-lg p-8 w-full max-w-md aspect-[4/3] flex items-center justify-center">
                <div className="grid grid-cols-2 gap-4 w-full">
                  <div className="aspect-square rounded-md bg-white/20 flex items-center justify-center">
                    <div className="text-white/90 w-2/3 h-2/3">
                      <svg viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-1-13h2v6h-2zm0 8h2v2h-2z"/>
                      </svg>
                    </div>
                  </div>
                  <div className="aspect-square rounded-md bg-white/20 flex items-center justify-center">
                    <div className="text-white/90 w-2/3 h-2/3">
                      <svg viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                        <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V5h14v14z"/>
                        <path d="M7 12h2v5H7zm8-5h2v10h-2zm-4 7h2v3h-2zm0-4h2v2h-2z"/>
                      </svg>
                    </div>
                  </div>
                  <div className="aspect-square rounded-md bg-white/20 flex items-center justify-center">
                    <div className="text-white/90 w-2/3 h-2/3">
                      <svg viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                        <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                      </svg>
                    </div>
                  </div>
                  <div className="aspect-square rounded-md bg-white/20 flex items-center justify-center">
                    <div className="text-white/90 w-2/3 h-2/3">
                      <svg viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                        <path d="M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z"/>
                      </svg>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

function FeatureCard({ number, title, description, component }: {
  number: number;
  title: string;
  description: string;
  component: React.ReactNode;
}) {
  return (
    <div className="flex flex-col items-center p-6 rounded-lg">
      <div className="w-10 h-10 rounded-full bg-primary flex items-center justify-center text-primary-foreground font-semibold mb-6">
        {number}
      </div>
      <h3 className="text-xl font-semibold mb-4">{title}</h3>
      <p className="text-muted-foreground text-center mb-6">{description}</p>
      <div className="rounded-lg overflow-hidden">
        {component}
      </div>
    </div>
  );
}
