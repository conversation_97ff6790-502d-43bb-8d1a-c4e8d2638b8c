from pydantic_settings import BaseSettings
from typing import Optional
import os
import secrets


class Settings(BaseSettings):
    # 应用基础配置
    APP_NAME: str = "SteamPY API"
    APP_VERSION: str = "1.0.0"
    DEBUG: bool = True
    
    # 数据库配置 - 使用SQLite
    DATABASE_URL: str = "sqlite:///./steampy.db"
    
    # Redis配置 - 可选，如果没有Redis就使用内存缓存
    REDIS_URL: Optional[str] = None
    
    # JWT配置 - 安全性改进
    SECRET_KEY: str = os.getenv("SECRET_KEY", secrets.token_urlsafe(32))
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7
    ALGORITHM: str = "HS256"
    
    # Steam API配置
    STEAM_API_KEY: Optional[str] = None
    STEAM_WEB_API_URL: str = "https://api.steampowered.com"
    
    # 支付配置
    ALIPAY_APP_ID: Optional[str] = None
    ALIPAY_PRIVATE_KEY: Optional[str] = None
    ALIPAY_PUBLIC_KEY: Optional[str] = None
    
    # 邮件配置
    SMTP_SERVER: Optional[str] = None
    SMTP_PORT: int = 587
    SMTP_USERNAME: Optional[str] = None
    SMTP_PASSWORD: Optional[str] = None
    
    # 文件上传配置
    UPLOAD_DIR: str = "uploads"
    MAX_FILE_SIZE: int = 5 * 1024 * 1024  # 5MB
    
    # CORS配置 - 安全性改进
    ALLOWED_ORIGINS: list[str] = [
        "http://localhost:3000",
        "http://127.0.0.1:3000",
        "http://localhost:8000",
        "http://127.0.0.1:8000"
    ] if not DEBUG else [
        "http://localhost:3000",
        "http://127.0.0.1:3000",
        "http://localhost:8000", 
        "http://127.0.0.1:8000",
        "*"  # 仅在开发环境允许所有来源
    ]
    
    # 速率限制配置
    RATE_LIMIT_ENABLED: bool = True
    RATE_LIMIT_REQUESTS: int = 100  # 每分钟请求数
    RATE_LIMIT_WINDOW: int = 60  # 时间窗口（秒）
    
    # 安全配置
    PASSWORD_MIN_LENGTH: int = 8
    PASSWORD_REQUIRE_UPPERCASE: bool = False
    PASSWORD_REQUIRE_LOWERCASE: bool = False
    PASSWORD_REQUIRE_NUMBERS: bool = False
    PASSWORD_REQUIRE_SYMBOLS: bool = False
    
    # Celery配置 - 可选
    CELERY_BROKER_URL: Optional[str] = None
    CELERY_RESULT_BACKEND: Optional[str] = None
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# 创建全局设置实例
settings = Settings()


# 数据库配置
class DatabaseConfig:
    @staticmethod
    def get_database_url() -> str:
        return settings.DATABASE_URL
    
    @staticmethod
    def get_database_url_sync() -> str:
        return settings.DATABASE_URL


# Redis配置
class RedisConfig:
    @staticmethod
    def get_redis_url() -> Optional[str]:
        return settings.REDIS_URL
    
    @staticmethod
    def is_redis_available() -> bool:
        return settings.REDIS_URL is not None 