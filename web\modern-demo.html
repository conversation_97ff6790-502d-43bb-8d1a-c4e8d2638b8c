<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>现代化设计演示 - SteamPY</title>
    <link rel="stylesheet" href="/static/main.css">
    <link rel="stylesheet" href="/static/modern-theme.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .demo-section {
            padding: var(--space-16) 0;
        }
        
        .component-showcase {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: var(--space-8);
            margin: var(--space-8) 0;
        }
        
        .showcase-item {
            background: white;
            border-radius: var(--radius-xl);
            padding: var(--space-6);
            box-shadow: var(--shadow-md);
        }
        
        .showcase-title {
            font-size: 1.125rem;
            font-weight: 600;
            margin-bottom: var(--space-4);
            color: var(--gray-900);
        }
        
        .color-palette {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: var(--space-2);
            margin: var(--space-4) 0;
        }
        
        .color-swatch {
            height: 60px;
            border-radius: var(--radius-lg);
            display: flex;
            align-items: end;
            padding: var(--space-2);
            color: white;
            font-size: 0.75rem;
            font-weight: 500;
            text-shadow: 0 1px 2px rgba(0,0,0,0.5);
        }
        
        .glassmorphism {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: var(--radius-2xl);
            padding: var(--space-6);
        }
        
        .gradient-text {
            background: linear-gradient(135deg, var(--steam-green) 0%, var(--steam-blue) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .floating-card {
            background: white;
            border-radius: var(--radius-2xl);
            padding: var(--space-6);
            box-shadow: var(--shadow-xl);
            position: relative;
            overflow: hidden;
        }
        
        .floating-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--steam-green) 0%, var(--steam-blue) 100%);
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--space-4);
        }
        
        .metric-card {
            background: linear-gradient(135deg, var(--steam-green) 0%, var(--steam-green-light) 100%);
            color: white;
            padding: var(--space-6);
            border-radius: var(--radius-xl);
            text-align: center;
        }
        
        .metric-value {
            font-size: 2rem;
            font-weight: 800;
            margin-bottom: var(--space-2);
        }
        
        .metric-label {
            font-size: 0.875rem;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <!-- 导航栏容器 -->
    <div id="navbar-container"></div>

    <!-- 现代化英雄区域 -->
    <section class="hero-modern">
        <div class="hero-content-modern">
            <h1 class="hero-title-modern">
                现代化设计系统
            </h1>
            <p class="hero-subtitle-modern">
                基于最新设计趋势，打造极致用户体验的Steam交易平台界面
            </p>
            <div style="display: flex; gap: var(--space-4); justify-content: center; flex-wrap: wrap;">
                <button class="btn btn-primary btn-xl">
                    <i class="fas fa-rocket"></i>
                    开始体验
                </button>
                <button class="btn btn-outline btn-xl" style="border-color: white; color: white;">
                    <i class="fas fa-code"></i>
                    查看代码
                </button>
            </div>
        </div>
    </section>

    <!-- 指标展示 -->
    <section class="demo-section" style="background: var(--gray-100);">
        <div class="container">
            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-value">500K+</div>
                    <div class="metric-label">用户信赖</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">99.9%</div>
                    <div class="metric-label">成功率</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">24/7</div>
                    <div class="metric-label">客服支持</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">7年</div>
                    <div class="metric-label">行业经验</div>
                </div>
            </div>
        </div>
    </section>

    <!-- 组件展示 -->
    <section class="demo-section">
        <div class="container">
            <h2 style="text-align: center; font-size: 2.5rem; margin-bottom: var(--space-12);" class="gradient-text">
                现代化组件系统
            </h2>
            
            <div class="component-showcase">
                <!-- 按钮组件 -->
                <div class="showcase-item">
                    <h3 class="showcase-title">按钮组件</h3>
                    <div style="display: flex; flex-direction: column; gap: var(--space-3);">
                        <button class="btn btn-primary">主要按钮</button>
                        <button class="btn btn-secondary">次要按钮</button>
                        <button class="btn btn-outline">边框按钮</button>
                        <button class="btn btn-ghost">幽灵按钮</button>
                        <div style="display: flex; gap: var(--space-2);">
                            <button class="btn btn-primary btn-sm">小</button>
                            <button class="btn btn-primary">中</button>
                            <button class="btn btn-primary btn-lg">大</button>
                        </div>
                    </div>
                </div>

                <!-- 状态指示器 -->
                <div class="showcase-item">
                    <h3 class="showcase-title">状态指示器</h3>
                    <div style="display: flex; flex-direction: column; gap: var(--space-3);">
                        <span class="status-modern status-success-modern">
                            <i class="fas fa-check"></i>
                            成功
                        </span>
                        <span class="status-modern status-warning-modern">
                            <i class="fas fa-exclamation-triangle"></i>
                            警告
                        </span>
                        <span class="status-modern status-error-modern">
                            <i class="fas fa-times"></i>
                            错误
                        </span>
                        <span class="status-modern status-info-modern">
                            <i class="fas fa-info"></i>
                            信息
                        </span>
                    </div>
                </div>

                <!-- 输入框 -->
                <div class="showcase-item">
                    <h3 class="showcase-title">表单控件</h3>
                    <div style="display: flex; flex-direction: column; gap: var(--space-3);">
                        <input type="text" class="input" placeholder="用户名或邮箱">
                        <input type="password" class="input" placeholder="密码">
                        <input type="email" class="input" placeholder="邮箱地址">
                        <select class="input">
                            <option>选择游戏类型</option>
                            <option>动作游戏</option>
                            <option>角色扮演</option>
                        </select>
                    </div>
                </div>

                <!-- 卡片组件 -->
                <div class="showcase-item">
                    <h3 class="showcase-title">卡片组件</h3>
                    <div class="card">
                        <div class="card-header">
                            <h4 style="margin: 0;">卡片标题</h4>
                        </div>
                        <div class="card-body">
                            <p style="margin: 0; color: var(--gray-600);">
                                这是一个现代化的卡片组件示例，具有优雅的阴影和圆角设计。
                            </p>
                        </div>
                        <div class="card-footer">
                            <button class="btn btn-primary btn-sm">操作</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 产品卡片示例 -->
            <h3 style="font-size: 1.5rem; margin: var(--space-12) 0 var(--space-6);">现代化产品卡片</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: var(--space-6);">
                <div class="product-card-modern">
                    <div class="product-image-modern">
                        <img src="https://images.unsplash.com/photo-1552820728-8b83bb6b773f?w=400&h=225&fit=crop" alt="游戏">
                        <div class="product-badge-modern">-75%</div>
                    </div>
                    <div class="product-info-modern">
                        <h4 class="product-title-modern">赛博朋克 2077</h4>
                        <p class="product-description-modern">在这个开放世界的反乌托邦中，体验充满活力的夜之城...</p>
                        <div class="product-price-modern">
                            <span class="price-current-modern">¥149</span>
                            <span class="price-original-modern">¥596</span>
                        </div>
                        <button class="btn btn-primary" style="width: 100%;">
                            <i class="fas fa-shopping-cart"></i>
                            立即购买
                        </button>
                    </div>
                </div>

                <div class="product-card-modern">
                    <div class="product-image-modern">
                        <img src="https://images.unsplash.com/photo-1511512578047-dfb367046420?w=400&h=225&fit=crop" alt="游戏">
                        <div class="product-badge-modern" style="background: linear-gradient(135deg, var(--steam-green) 0%, var(--steam-green-light) 100%);">推荐</div>
                    </div>
                    <div class="product-info-modern">
                        <h4 class="product-title-modern">Steam钱包充值</h4>
                        <p class="product-description-modern">快速安全的Steam钱包充值服务，支持多种支付方式...</p>
                        <div class="product-price-modern">
                            <span class="price-current-modern">¥100</span>
                        </div>
                        <button class="btn btn-primary" style="width: 100%;">
                            <i class="fas fa-wallet"></i>
                            立即充值
                        </button>
                    </div>
                </div>
            </div>

            <!-- 特性卡片 -->
            <h3 style="font-size: 1.5rem; margin: var(--space-12) 0 var(--space-6);">特性展示</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: var(--space-6);">
                <div class="feature-card-modern">
                    <div class="feature-icon-modern">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h4 style="font-size: 1.125rem; font-weight: 600; margin-bottom: var(--space-2);">安全保障</h4>
                    <p style="color: var(--gray-600); line-height: 1.6;">采用银行级安全加密，保护您的账户和资金安全</p>
                </div>

                <div class="feature-card-modern">
                    <div class="feature-icon-modern">
                        <i class="fas fa-bolt"></i>
                    </div>
                    <h4 style="font-size: 1.125rem; font-weight: 600; margin-bottom: var(--space-2);">快速交付</h4>
                    <p style="color: var(--gray-600); line-height: 1.6;">自动化处理系统，90%订单5分钟内完成</p>
                </div>

                <div class="feature-card-modern">
                    <div class="feature-icon-modern">
                        <i class="fas fa-headset"></i>
                    </div>
                    <h4 style="font-size: 1.125rem; font-weight: 600; margin-bottom: var(--space-2);">24/7支持</h4>
                    <p style="color: var(--gray-600); line-height: 1.6;">专业客服团队全天候为您服务，问题快速解决</p>
                </div>
            </div>

            <!-- 玻璃态效果 -->
            <div style="margin: var(--space-16) 0; background: linear-gradient(135deg, var(--steam-blue) 0%, var(--steam-green) 100%); border-radius: var(--radius-2xl); padding: var(--space-12); position: relative; overflow: hidden;">
                <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: url('https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=1200&h=600&fit=crop') center/cover; opacity: 0.1;"></div>
                <div class="glassmorphism" style="position: relative; z-index: 1; text-align: center;">
                    <h3 style="color: white; font-size: 1.5rem; margin-bottom: var(--space-4);">玻璃态设计</h3>
                    <p style="color: rgba(255,255,255,0.9); margin-bottom: var(--space-6);">采用现代玻璃态设计，带来更加优雅的视觉体验</p>
                    <button class="btn btn-outline" style="border-color: white; color: white;">
                        了解更多
                    </button>
                </div>
            </div>

            <!-- 浮动卡片效果 -->
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: var(--space-6); margin: var(--space-12) 0;">
                <div class="floating-card">
                    <h4 style="font-size: 1.25rem; font-weight: 600; margin-bottom: var(--space-4);">实时数据</h4>
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: var(--space-3);">
                        <span>今日订单</span>
                        <span style="font-weight: 600; color: var(--steam-green);">+23%</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: var(--space-3);">
                        <span>用户增长</span>
                        <span style="font-weight: 600; color: var(--steam-green);">+15%</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <span>客户满意度</span>
                        <span style="font-weight: 600; color: var(--steam-green);">99.5%</span>
                    </div>
                </div>

                <div class="floating-card">
                    <h4 style="font-size: 1.25rem; font-weight: 600; margin-bottom: var(--space-4);">快速操作</h4>
                    <div style="display: flex; flex-direction: column; gap: var(--space-3);">
                        <button class="btn btn-ghost" style="justify-content: flex-start;">
                            <i class="fas fa-plus"></i>
                            添加产品
                        </button>
                        <button class="btn btn-ghost" style="justify-content: flex-start;">
                            <i class="fas fa-chart-bar"></i>
                            查看统计
                        </button>
                        <button class="btn btn-ghost" style="justify-content: flex-start;">
                            <i class="fas fa-cog"></i>
                            系统设置
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 页脚 -->
    <footer style="background: var(--gray-900); color: white; padding: var(--space-12) 0; text-align: center;">
        <div class="container">
            <h3 style="margin-bottom: var(--space-4); font-size: 1.5rem;">SteamPY 现代化设计系统</h3>
            <p style="opacity: 0.8; margin-bottom: var(--space-6);">基于最新设计趋势，为Steam交易平台打造的现代化UI组件库</p>
            <div style="display: flex; justify-content: center; gap: var(--space-6); margin-bottom: var(--space-8);">
                <a href="#" style="color: var(--gray-400); text-decoration: none;"><i class="fab fa-github"></i> GitHub</a>
                <a href="#" style="color: var(--gray-400); text-decoration: none;"><i class="fab fa-figma"></i> Figma</a>
                <a href="#" style="color: var(--gray-400); text-decoration: none;"><i class="fas fa-book"></i> 文档</a>
            </div>
            <p style="opacity: 0.6; font-size: 0.875rem;">© 2024 SteamPY. 保留所有权利。</p>
        </div>
    </footer>

    <!-- 脚本 -->
    <script src="/scripts/navigation.js"></script>
    <script>
        // 简单的交互效果
        document.addEventListener('DOMContentLoaded', function() {
            // 添加滚动动画
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            });

            // 观察所有卡片元素
            document.querySelectorAll('.showcase-item, .product-card-modern, .feature-card-modern').forEach(el => {
                el.style.opacity = '0';
                el.style.transform = 'translateY(20px)';
                el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                observer.observe(el);
            });

            // 添加按钮点击效果
            document.querySelectorAll('.btn').forEach(btn => {
                btn.addEventListener('click', function(e) {
                    const ripple = document.createElement('span');
                    const rect = this.getBoundingClientRect();
                    const size = Math.max(rect.width, rect.height);
                    const x = e.clientX - rect.left - size / 2;
                    const y = e.clientY - rect.top - size / 2;
                    
                    ripple.style.cssText = `
                        position: absolute;
                        border-radius: 50%;
                        background: rgba(255,255,255,0.3);
                        transform: scale(0);
                        animation: ripple 0.6s ease-out;
                        width: ${size}px;
                        height: ${size}px;
                        left: ${x}px;
                        top: ${y}px;
                        pointer-events: none;
                    `;
                    
                    this.style.position = 'relative';
                    this.style.overflow = 'hidden';
                    this.appendChild(ripple);
                    
                    setTimeout(() => ripple.remove(), 600);
                });
            });
        });

        // 添加 CSS 动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes ripple {
                to {
                    transform: scale(4);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html> 