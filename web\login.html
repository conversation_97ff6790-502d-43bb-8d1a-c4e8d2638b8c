<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户登录 - SteamPY</title>
    <link rel="stylesheet" href="/static/main.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .login-container {
            min-height: 100vh;
            background: linear-gradient(135deg, var(--steam-blue) 0%, var(--steam-lightblue) 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        .login-box {
            background: white;
            border-radius: 16px;
            padding: 40px;
            width: 100%;
            max-width: 400px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.2);
        }
        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .login-title {
            font-size: 2rem;
            margin-bottom: 10px;
            color: #2c3e50;
        }
        .login-subtitle {
            color: #6c757d;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
        }
        .form-input {
            width: 100%;
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }
        .form-input:focus {
            outline: none;
            border-color: var(--steam-green);
        }
        .form-options {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            font-size: 14px;
        }
        .remember-me {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .forgot-password {
            color: var(--steam-green);
            text-decoration: none;
        }
        .forgot-password:hover {
            text-decoration: underline;
        }
        .login-btn {
            width: 100%;
            margin-bottom: 20px;
        }
        .divider {
            text-align: center;
            margin: 30px 0;
            position: relative;
            color: #6c757d;
        }
        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #ddd;
        }
        .divider span {
            background: white;
            padding: 0 20px;
        }
        .social-login {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }
        .social-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            text-decoration: none;
            color: #2c3e50;
            transition: all 0.3s ease;
        }
        .social-btn:hover {
            border-color: var(--steam-green);
            background: rgba(144, 186, 60, 0.1);
        }
        .register-prompt {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }
        .register-link {
            color: var(--steam-green);
            text-decoration: none;
            font-weight: 600;
        }
        .register-link:hover {
            text-decoration: underline;
        }
        .back-home {
            position: absolute;
            top: 20px;
            left: 20px;
            color: white;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 10px 15px;
            border-radius: 8px;
            background: rgba(255,255,255,0.1);
            transition: background 0.3s ease;
        }
        .back-home:hover {
            background: rgba(255,255,255,0.2);
            color: white;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <a href="/" class="back-home">
            <i class="fas fa-arrow-left"></i>
            返回首页
        </a>
        
        <div class="login-box">
            <div class="login-header">
                <h1 class="login-title">欢迎回来</h1>
                <p class="login-subtitle">登录您的SteamPY账户</p>
            </div>
            
            <form id="login-form">
                <div class="form-group">
                    <label for="username">用户名或邮箱</label>
                    <input type="text" id="username" class="form-input" placeholder="请输入用户名或邮箱" required>
                </div>
                
                <div class="form-group">
                    <label for="password">密码</label>
                    <input type="password" id="password" class="form-input" placeholder="请输入密码" required>
                </div>
                
                <div class="form-options">
                    <div class="remember-me">
                        <input type="checkbox" id="remember" name="remember">
                        <label for="remember">记住我</label>
                    </div>
                    <a href="#" class="forgot-password">忘记密码？</a>
                </div>
                
                <button type="submit" class="btn btn-primary login-btn">登录</button>
            </form>
            
            <div class="divider">
                <span>或者</span>
            </div>
            
            <div class="social-login">
                <a href="#" class="social-btn" data-provider="steam">
                    <i class="fab fa-steam" style="color: #171a21;"></i>
                    使用Steam账户登录
                </a>
                <a href="#" class="social-btn" data-provider="qq">
                    <i class="fab fa-qq" style="color: #1296db;"></i>
                    使用QQ登录
                </a>
                <a href="#" class="social-btn" data-provider="wechat">
                    <i class="fab fa-weixin" style="color: #09bb07;"></i>
                    使用微信登录
                </a>
            </div>
            
            <div class="register-prompt">
                <p>还没有账户？<a href="/register" class="register-link">立即注册</a></p>
            </div>
        </div>
    </div>

    <script src="/scripts/main.js"></script>
    <script src="/scripts/redirect-fix.js"></script>
    <script>
        // 备用showMessage函数，防止main.js加载失败
        if (typeof window.showMessage === 'undefined') {
            window.showMessage = function(message, type = 'info') {
                const messageDiv = document.createElement('div');
                messageDiv.textContent = message;
                messageDiv.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    padding: 15px 20px;
                    background: ${type === 'success' ? '#2ecc71' : type === 'error' ? '#e74c3c' : '#3498db'};
                    color: white;
                    border-radius: 5px;
                    z-index: 1000;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                `;
                document.body.appendChild(messageDiv);
                setTimeout(() => messageDiv.remove(), 3000);
            };
        }
        
        document.addEventListener('DOMContentLoaded', function() {
            const loginForm = document.getElementById('login-form');
            
            loginForm.addEventListener('submit', async function(e) {
                e.preventDefault();
                
                const username = document.getElementById('username').value.trim();
                const password = document.getElementById('password').value;
                const remember = document.getElementById('remember').checked;
                
                if (!username || !password) {
                    showMessage('请填写完整的登录信息', 'error');
                    return;
                }
                
                // 发送真实登录请求
                const loginBtn = this.querySelector('.login-btn');
                loginBtn.textContent = '登录中...';
                loginBtn.disabled = true;
                
                try {
                    // 使用FormData格式，因为后端期望OAuth2PasswordRequestForm
                    const formData = new FormData();
                    formData.append('username', username);
                    formData.append('password', password);
                    
                    console.log('发送登录请求:', { username, password: '***' });
                    
                    const response = await fetch('/api/v1/auth/login', {
                        method: 'POST',
                        body: formData
                    });
                    
                    const result = await response.json();
                    console.log('登录响应:', result);
                    
                    if (response.ok && result.access_token) {
                        // 保存令牌到localStorage
                        localStorage.setItem('access_token', result.access_token);
                        localStorage.setItem('refresh_token', result.refresh_token);
                        localStorage.setItem('token_type', result.token_type);
                        
                        // 获取用户信息
                        try {
                            const profileResponse = await fetch('/api/v1/auth/profile', {
                                headers: {
                                    'Authorization': `Bearer ${result.access_token}`
                                }
                            });
                            
                            if (profileResponse.ok) {
                                const userProfile = await profileResponse.json();
                                console.log('用户信息:', userProfile);
                                
                                // 保存用户信息到localStorage
                                localStorage.setItem('user_info', JSON.stringify({
                                    username: userProfile.username,
                                    email: userProfile.email,
                                    id: userProfile.id,
                                    nickname: userProfile.nickname,
                                    avatar_url: userProfile.avatar_url,
                                    balance: userProfile.balance
                                }));
                                
                                // 更新authManager状态
                                if (typeof authManager !== 'undefined') {
                                    authManager.login({
                                        username: userProfile.username,
                                        email: userProfile.email,
                                        userId: userProfile.id,
                                        nickname: userProfile.nickname,
                                        avatar_url: userProfile.avatar_url,
                                        balance: userProfile.balance
                                    });
                                }
                                
                                // 更新导航栏显示
                                if (typeof navigation !== 'undefined' && navigation.updateUser) {
                                    navigation.updateUser();
                                }
                            }
                        } catch (profileError) {
                            console.error('获取用户信息失败:', profileError);
                        }
                        
                        showMessage('登录成功！正在跳转...', 'success');
                        
                        // 使用智能跳转函数，会自动判断应该跳转到哪里
                        if (typeof window.smartRedirect === 'function') {
                            window.smartRedirect('/', '/');
                        } else {
                            // 备用方案
                            setTimeout(() => {
                                console.log('使用备用跳转方案...');
                                window.location.replace('/');
                            }, 1500);
                        }
                    } else {
                        // 处理错误
                        const errorMessage = result.detail || result.message || '登录失败，请检查用户名和密码';
                        showMessage(errorMessage, 'error');
                        console.error('登录失败:', result);
                    }
                } catch (error) {
                    console.error('登录请求失败:', error);
                    showMessage('网络错误，请检查网络连接后重试', 'error');
                } finally {
                    // 恢复按钮状态
                    loginBtn.textContent = '登录';
                    loginBtn.disabled = false;
                }
            });
            
            // 社交登录
            document.querySelectorAll('.social-btn').forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    const provider = this.dataset.provider;
                    showMessage(`正在跳转到${provider}登录...`, 'info');
                    
                    // 这里可以集成实际的第三方登录
                    setTimeout(() => {
                        showMessage('第三方登录功能开发中...', 'info');
                    }, 1000);
                });
            });
        });
    </script>
</body>
</html> 