from fastapi import APIRouter, Depends, HTTPException, status
from app.models.user import User
from app.api.v1.endpoints.auth import get_current_user

router = APIRouter()


@router.get("/dashboard")
async def admin_dashboard(current_user: User = Depends(get_current_user)):
    """管理员仪表板 - 待实现"""
    if current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Permission denied"
        )
    
    return {
        "message": "Admin dashboard - Coming Soon",
        "user": current_user.username,
        "role": current_user.role
    }


@router.get("/stats")
async def get_stats(current_user: User = Depends(get_current_user)):
    """获取统计信息 - 待实现"""
    if current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Permission denied"
        )
    
    return {
        "total_users": 0,
        "total_orders": 0,
        "total_revenue": 0,
        "message": "Stats API - Coming Soon"
    } 