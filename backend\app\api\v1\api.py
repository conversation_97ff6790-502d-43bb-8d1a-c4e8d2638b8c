from fastapi import APIRouter
from .endpoints import auth, users, products, orders, steam, admin

api_router = APIRouter()

# 认证相关路由
api_router.include_router(auth.router, prefix="/auth", tags=["认证"])

# 用户相关路由
api_router.include_router(users.router, prefix="/users", tags=["用户管理"])

# 产品相关路由
api_router.include_router(products.router, prefix="/products", tags=["产品管理"])

# 订单相关路由
api_router.include_router(orders.router, prefix="/orders", tags=["订单管理"])

# Steam相关路由
api_router.include_router(steam.router, prefix="/steam", tags=["Steam集成"])

# 管理员相关路由
api_router.include_router(admin.router, prefix="/admin", tags=["管理员功能"]) 