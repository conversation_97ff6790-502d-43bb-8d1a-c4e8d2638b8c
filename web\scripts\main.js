// 工具函数：显示消息 - 移到全局作用域确保立即可用
window.showMessage = function(message, type = 'info') {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message message-${type}`;
    messageDiv.textContent = message;
    messageDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        background: ${type === 'success' ? '#2ecc71' : type === 'error' ? '#e74c3c' : '#3498db'};
        color: white;
        border-radius: 5px;
        z-index: 1000;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        animation: slideIn 0.3s ease-out;
        max-width: 350px;
        word-wrap: break-word;
    `;
    
    // 添加动画样式
    if (!document.getElementById('message-styles')) {
        const style = document.createElement('style');
        style.id = 'message-styles';
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
            .message-exit {
                animation: slideOut 0.3s ease-in forwards;
            }
        `;
        document.head.appendChild(style);
    }
    
    document.body.appendChild(messageDiv);
    
    // 3秒后移除消息
    setTimeout(() => {
        messageDiv.classList.add('message-exit');
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.remove();
            }
        }, 300);
    }, 3000);
};

// 工具函数：格式化价格
window.formatPrice = function(price) {
    return '¥' + price.toFixed(2);
};

// 网络状态检测
window.isOnline = function() {
    return navigator.onLine;
};

// 全局错误处理
window.addEventListener('error', function(event) {
    console.error('全局错误:', event.error);
    if (!window.isOnline()) {
        showMessage('网络连接已断开，请检查网络设置', 'error');
    }
});

// 网络状态变化监听
window.addEventListener('online', function() {
    showMessage('网络连接已恢复', 'success');
});

window.addEventListener('offline', function() {
    showMessage('网络连接已断开', 'error');
});

// 改进的fetch包装函数
window.safeFetch = async function(url, options = {}) {
    // 检查网络状态
    if (!window.isOnline()) {
        throw new Error('网络连接不可用');
    }
    
    // 设置默认超时
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10秒超时
    
    try {
        const response = await fetch(url, {
            ...options,
            signal: controller.signal
        });
        clearTimeout(timeoutId);
        return response;
    } catch (error) {
        clearTimeout(timeoutId);
        if (error.name === 'AbortError') {
            throw new Error('请求超时，请重试');
        }
        throw error;
    }
};

// 主要JavaScript功能
document.addEventListener('DOMContentLoaded', function() {
    
    // 倒计时功能
    function updateCountdown() {
        const countdownElement = document.getElementById('countdown-timer');
        if (!countdownElement) return;
        
        // 模拟倒计时（每秒递减）
        let time = parseInt(localStorage.getItem('countdownTime')) || 86400; // 24小时
        
        function tick() {
            const hours = Math.floor(time / 3600);
            const minutes = Math.floor((time % 3600) / 60);
            const seconds = time % 60;
            
            countdownElement.textContent = 
                `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            
            time--;
            if (time < 0) {
                time = 86400; // 重置为24小时
            }
            
            localStorage.setItem('countdownTime', time.toString());
        }
        
        tick(); // 立即执行一次
        setInterval(tick, 1000); // 每秒更新
    }
    
    // 启动倒计时
    updateCountdown();
    
    // 添加产品卡片点击事件
    document.querySelectorAll('.product-card').forEach(card => {
        card.addEventListener('click', function() {
            // 可以添加产品详情查看功能
            console.log('点击了产品卡片');
        });
    });
    
    // 分类卡片悬停效果
    document.querySelectorAll('.category-card').forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-8px)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
    
    // 平滑滚动
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
    
    // 移动端导航菜单切换
    function createMobileMenu() {
        const navbar = document.querySelector('.navbar .container');
        const navMenu = document.querySelector('.nav-menu');
        
        // 创建汉堡菜单按钮
        const menuToggle = document.createElement('button');
        menuToggle.className = 'mobile-menu-toggle';
        menuToggle.innerHTML = '☰';
        menuToggle.style.cssText = `
            display: none;
            background: none;
            border: none;
            color: white;
            font-size: 1.5rem;
            cursor: pointer;
            @media (max-width: 768px) {
                display: block;
            }
        `;
        
        // 添加到导航栏
        navbar.appendChild(menuToggle);
        
        // 切换菜单显示
        menuToggle.addEventListener('click', function() {
            navMenu.style.display = navMenu.style.display === 'flex' ? 'none' : 'flex';
        });
        
        // 响应式处理
        window.addEventListener('resize', function() {
            if (window.innerWidth > 768) {
                navMenu.style.display = 'flex';
                menuToggle.style.display = 'none';
            } else {
                navMenu.style.display = 'none';
                menuToggle.style.display = 'block';
            }
        });
    }
    
    // 初始化移动端菜单
    createMobileMenu();
    
    // 页面加载动画
    function addLoadAnimation() {
        const cards = document.querySelectorAll('.feature-card, .product-card, .category-card, .testimonial-card');
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, { threshold: 0.1 });
        
        cards.forEach(card => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(card);
        });
    }
    
    // 添加加载动画
    addLoadAnimation();
    
    // API状态检查
    function checkAPIStatus() {
        // 检查后端API是否可用
        fetch('/health')
            .then(response => response.json())
            .then(data => {
                console.log('API状态正常:', data);
            })
            .catch(error => {
                console.log('API连接失败，使用模拟数据');
            });
    }
    
    // 检查API状态
    checkAPIStatus();

    // 初始化用户认证管理器
    if (typeof authManager !== 'undefined') {
        authManager.init();
    }
    
    // 初始化导航栏
    if (typeof navigation !== 'undefined') {
        navigation.init();
    }
    
    // 检查网络状态
    if (!window.isOnline()) {
        showMessage('当前处于离线状态', 'error');
    }
    
    // 页面加载性能监控
    if (window.performance && window.performance.timing) {
        const loadTime = window.performance.timing.loadEventEnd - window.performance.timing.navigationStart;
        if (loadTime > 3000) { // 加载时间超过3秒
            console.warn('页面加载时间较长:', loadTime + 'ms');
        }
    }
});

// 用户认证状态管理
class AuthManager {
    constructor() {
        this.isLoggedIn = localStorage.getItem('isLoggedIn') === 'true';
        this.userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
    }
    
    login(userInfo) {
        this.isLoggedIn = true;
        this.userInfo = userInfo;
        localStorage.setItem('isLoggedIn', 'true');
        localStorage.setItem('userInfo', JSON.stringify(userInfo));
        this.updateUI();
    }
    
    logout() {
        this.isLoggedIn = false;
        this.userInfo = {};
        localStorage.removeItem('isLoggedIn');
        localStorage.removeItem('userInfo');
        this.updateUI();
    }
    
    updateUI() {
        const navActions = document.querySelector('.nav-actions');
        if (!navActions) return;
        
        if (this.isLoggedIn) {
            navActions.innerHTML = `
                <span class="user-welcome">欢迎，${this.userInfo.username || '用户'}</span>
                <a href="profile.html" class="nav-link">个人中心</a>
                <button onclick="authManager.logout()" class="btn btn-outline">退出</button>
            `;
        } else {
            navActions.innerHTML = `
                <a href="profile.html" class="nav-link">个人中心</a>
                <a href="login.html" class="btn btn-primary">登录</a>
                <a href="register.html" class="btn btn-outline">注册</a>
            `;
        }
    }
}

// 全局认证管理器
const authManager = new AuthManager(); 