from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from .config import settings
import os

# 确保数据库目录存在
db_dir = os.path.dirname(settings.DATABASE_URL.replace("sqlite:///", ""))
if db_dir and not os.path.exists(db_dir):
    os.makedirs(db_dir)

# 创建数据库引擎 - SQLite配置
engine = create_engine(
    settings.DATABASE_URL,
    connect_args={"check_same_thread": False},  # SQLite特定配置
    pool_pre_ping=True,
    echo=settings.DEBUG  # 开发环境显示SQL语句
)

# 创建会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def get_db():
    """获取数据库会话"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def create_tables():
    """创建所有表"""
    # 重要：确保所有模型都被导入，这样SQLAlchemy才能发现它们
    from app.models.user import User, UserSession
    from app.models.product import Product, Order, CDKInventory
    
    # 使用模型的元数据来创建表
    from app.models.base import Base
    
    print(f"🗄️  正在创建数据库表...")
    print(f"📋 发现的表: {list(Base.metadata.tables.keys())}")
    
    # 创建所有表
    Base.metadata.create_all(bind=engine)
    print("✅ 数据库表创建完成")


def init_db():
    """初始化数据库"""
    create_tables()
    
    # 创建默认管理员用户
    from app.models.user import User, UserRole, UserStatus
    from passlib.context import CryptContext
    
    pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
    
    db = SessionLocal()
    try:
        # 检查是否已有管理员用户
        admin_user = db.query(User).filter(User.role == UserRole.ADMIN).first()
        if not admin_user:
            # 创建默认管理员
            admin_user = User(
                username="admin",
                email="<EMAIL>",
                hashed_password=pwd_context.hash("admin123"),
                role=UserRole.ADMIN,
                status=UserStatus.ACTIVE,
                is_verified=True,
                nickname="系统管理员"
            )
            db.add(admin_user)
            db.commit()
            print("✅ 默认管理员账户创建完成")
            print("   用户名: admin")
            print("   密码: admin123")
        else:
            print("✅ 管理员账户已存在")
    except Exception as e:
        print(f"❌ 创建管理员账户失败: {e}")
        db.rollback()
    finally:
        db.close() 