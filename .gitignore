# ==========================================
# SteamPY 项目 .gitignore
# ==========================================

# ==========================================
# Python 后端相关
# ==========================================

# Python 字节码文件
__pycache__/
*.py[cod]
*$py.class

# 分发 / 打包
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# 单元测试 / 覆盖率报告
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# 虚拟环境
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder 项目设置
.spyderproject
.spyproject

# Rope 项目设置
.ropeproject

# mkdocs 文档
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre 类型检查器
.pyre/

# pytype 静态类型分析器
.pytype/

# Cython 调试符号
cython_debug/

# ==========================================
# Node.js / Next.js 前端相关
# ==========================================

# 依赖包
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# 运行时数据
pids
*.pid
*.seed
*.pid.lock

# 覆盖率目录
lib-cov

# nyc 测试覆盖率
.nyc_output

# Grunt 中间存储
.grunt

# Bower 依赖目录
bower_components

# node-waf 配置
.lock-wscript

# 编译的二进制插件
build/Release

# TypeScript 缓存
*.tsbuildinfo

# Next.js 构建输出
.next/
out/

# Nuxt.js 构建 / 生成输出
.nuxt
dist

# Gatsby 文件
.cache/
public

# Storybook 构建输出
.out
.storybook-out

# Temporary 文件夹
.tmp
.temp

# Vuepress 构建输出
.vuepress/dist

# Serverless 目录
.serverless/

# FuseBox 缓存
.fusebox/

# DynamoDB Local 文件
.dynamodb/

# TernJS 端口文件
.tern-port

# Stores VSCode 版本用于测试
.vscode-test

# yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# ==========================================
# 数据库文件
# ==========================================

# SQLite 数据库
*.db
*.sqlite
*.sqlite3
steampy.db
test.db

# PostgreSQL 数据文件
*.dump
*.sql

# Redis 数据文件
dump.rdb

# ==========================================
# 配置和环境文件
# ==========================================

# 环境配置文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 保留示例环境文件
!.env.example

# 配置文件
config.ini
config.yaml
config.json
settings.json

# 密钥文件
*.key
*.pem
*.crt
*.cert
keys/
certificates/

# ==========================================
# IDE 和编辑器
# ==========================================

# VSCode
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
*.code-workspace

# JetBrains IDEs
.idea/
*.swp
*.swo
*~

# Sublime Text
*.tmlanguage.cache
*.tmPreferences.cache
*.stTheme.cache
*.sublime-workspace
*.sublime-project

# Vim
*.swp
*.swo

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# ==========================================
# 操作系统
# ==========================================

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon?
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# ==========================================
# 日志文件
# ==========================================

# 通用日志
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# 应用日志
app.log
error.log
access.log
debug.log

# ==========================================
# Docker 相关
# ==========================================

# Docker 卷数据
docker-data/
.docker/

# ==========================================
# 备份和临时文件
# ==========================================

# 备份文件
*.bak
*.backup
*.old
*.orig
*.tmp
*.temp

# 压缩文件
*.zip
*.tar.gz
*.rar
*.7z

# ==========================================
# 项目特定文件
# ==========================================

# 上传文件目录
uploads/
media/
static/files/

# 缓存目录
cache/
.cache/

# 测试文件
test_*.py
*_test.py
test/

# 文档构建
docs/_build/
docs/build/

# 数据文件
data/
*.csv
*.json
*.xlsx
*.xls

# 证书和密钥
ssl/
certs/

# ==========================================
# 保留的配置文件示例
# ==========================================

# 保留示例配置文件
!config.example.json
!.env.example
!docker-compose.example.yml



/deeposter-clone.zip