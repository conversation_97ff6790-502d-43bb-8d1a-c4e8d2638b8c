<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>客服支持 - SteamPY</title>
    <link rel="stylesheet" href="/static/main.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="container">
            <div class="nav-brand">
                <h1>SteamPY</h1>
                <span class="nav-subtitle">Steam游戏交易平台</span>
            </div>
            
            <div class="nav-menu">
                <a href="index.html" class="nav-link">首页</a>
                <a href="products.html" class="nav-link">产品商城</a>
                <a href="wallet.html" class="nav-link">Steam钱包</a>
                <a href="orders.html" class="nav-link">我的订单</a>
                <a href="support.html" class="nav-link active">客服支持</a>
            </div>
            
            <div class="nav-actions">
                <a href="profile.html" class="nav-link">个人中心</a>
                <a href="login.html" class="btn btn-primary">登录</a>
                <a href="register.html" class="btn btn-outline">注册</a>
            </div>
        </div>
    </nav>

    <!-- 页面标题 -->
    <section class="page-header">
        <div class="container">
            <h1 class="page-title">🎧 客服支持</h1>
            <p class="page-subtitle">7x24小时专业客服，为您提供优质服务</p>
        </div>
    </section>

    <!-- 快速帮助 -->
    <section class="quick-help">
        <div class="container">
            <h2 class="section-title">快速帮助</h2>
            <div class="help-categories">
                <div class="help-category">
                    <div class="category-icon">💳</div>
                    <h3 class="category-title">充值问题</h3>
                    <p class="category-desc">充值失败、到账延迟、充值码无效等问题</p>
                    <a href="#faq-recharge" class="btn btn-outline">查看解答</a>
                </div>
                
                <div class="help-category">
                    <div class="category-icon">📦</div>
                    <h3 class="category-title">订单问题</h3>
                    <p class="category-desc">订单状态、退款申请、配送问题等</p>
                    <a href="#faq-orders" class="btn btn-outline">查看解答</a>
                </div>
                
                <div class="help-category">
                    <div class="category-icon">🔐</div>
                    <h3 class="category-title">账户安全</h3>
                    <p class="category-desc">账户被盗、密码找回、安全设置等</p>
                    <a href="#faq-security" class="btn btn-outline">查看解答</a>
                </div>
                
                <div class="help-category">
                    <div class="category-icon">🎮</div>
                    <h3 class="category-title">游戏相关</h3>
                    <p class="category-desc">激活码使用、游戏兑换、区域限制等</p>
                    <a href="#faq-games" class="btn btn-outline">查看解答</a>
                </div>
            </div>
        </div>
    </section>

    <!-- 联系客服 -->
    <section class="contact-support">
        <div class="container">
            <h2 class="section-title">联系客服</h2>
            <div class="contact-options">
                <div class="contact-option">
                    <div class="contact-icon">💬</div>
                    <h3 class="contact-title">在线客服</h3>
                    <p class="contact-desc">实时对话，快速解决问题</p>
                    <div class="contact-status online">客服在线</div>
                    <button class="btn btn-primary contact-btn" onclick="openLiveChat()">开始对话</button>
                </div>
                
                <div class="contact-option">
                    <div class="contact-icon">📞</div>
                    <h3 class="contact-title">电话客服</h3>
                    <p class="contact-desc">人工客服，专业解答</p>
                    <div class="contact-info">400-123-4567</div>
                    <div class="contact-time">服务时间：9:00-21:00</div>
                </div>
                
                <div class="contact-option">
                    <div class="contact-icon">✉️</div>
                    <h3 class="contact-title">邮箱支持</h3>
                    <p class="contact-desc">详细描述问题，24小时内回复</p>
                    <div class="contact-info"><EMAIL></div>
                    <button class="btn btn-outline contact-btn" onclick="openEmailForm()">发送邮件</button>
                </div>
                
                <div class="contact-option">
                    <div class="contact-icon">📱</div>
                    <h3 class="contact-title">QQ客服</h3>
                    <p class="contact-desc">添加客服QQ，随时咨询</p>
                    <div class="contact-info">QQ: 12345678</div>
                    <button class="btn btn-outline contact-btn">添加QQ</button>
                </div>
            </div>
        </div>
    </section>

    <!-- 工单系统 -->
    <section class="ticket-system">
        <div class="container">
            <h2 class="section-title">提交工单</h2>
            <div class="ticket-form-container">
                <div class="ticket-form">
                    <form id="ticket-form">
                        <div class="form-group">
                            <label for="ticket-type">问题类型</label>
                            <select id="ticket-type" class="form-input" required>
                                <option value="">请选择问题类型</option>
                                <option value="recharge">充值问题</option>
                                <option value="order">订单问题</option>
                                <option value="account">账户问题</option>
                                <option value="technical">技术问题</option>
                                <option value="suggestion">意见建议</option>
                                <option value="other">其他问题</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="ticket-subject">问题标题</label>
                            <input type="text" id="ticket-subject" class="form-input" placeholder="请简要描述您的问题" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="ticket-description">详细描述</label>
                            <textarea id="ticket-description" class="form-input" rows="6" placeholder="请详细描述您遇到的问题，包括操作步骤、错误信息等" required></textarea>
                        </div>
                        
                        <div class="form-group">
                            <label for="ticket-email">联系邮箱</label>
                            <input type="email" id="ticket-email" class="form-input" placeholder="用于接收回复的邮箱地址" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="ticket-phone">联系电话（可选）</label>
                            <input type="tel" id="ticket-phone" class="form-input" placeholder="方便紧急情况联系">
                        </div>
                        
                        <div class="form-group">
                            <label for="ticket-priority">紧急程度</label>
                            <select id="ticket-priority" class="form-input">
                                <option value="low">一般</option>
                                <option value="medium">较急</option>
                                <option value="high">紧急</option>
                            </select>
                        </div>
                        
                        <button type="submit" class="btn btn-primary btn-lg">提交工单</button>
                    </form>
                </div>
                
                <div class="ticket-info">
                    <h3>工单说明</h3>
                    <ul class="info-list">
                        <li>✓ 工单提交后，我们会在2小时内回复</li>
                        <li>✓ 您可以随时追踪工单处理进度</li>
                        <li>✓ 紧急问题建议直接联系在线客服</li>
                        <li>✓ 请提供详细信息以便快速定位问题</li>
                    </ul>
                    
                    <div class="response-time">
                        <h4>响应时间承诺</h4>
                        <div class="time-item">
                            <span class="priority high">紧急</span>
                            <span class="time">30分钟内</span>
                        </div>
                        <div class="time-item">
                            <span class="priority medium">较急</span>
                            <span class="time">2小时内</span>
                        </div>
                        <div class="time-item">
                            <span class="priority low">一般</span>
                            <span class="time">24小时内</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 常见问题FAQ -->
    <section class="faq-section">
        <div class="container">
            <h2 class="section-title">常见问题</h2>
            
            <!-- 充值问题 -->
            <div id="faq-recharge" class="faq-category">
                <h3 class="faq-category-title">💳 充值问题</h3>
                <div class="faq-list">
                    <div class="faq-item">
                        <div class="faq-question">
                            <span>充值后多久到账？</span>
                            <span class="faq-toggle">+</span>
                        </div>
                        <div class="faq-answer">
                            Steam钱包充值通常在3-5分钟内到账。如果超过10分钟未到账，请检查订单状态或联系客服。
                        </div>
                    </div>
                    
                    <div class="faq-item">
                        <div class="faq-question">
                            <span>充值失败了怎么办？</span>
                            <span class="faq-toggle">+</span>
                        </div>
                        <div class="faq-answer">
                            充值失败的款项会在24小时内自动退回到您的付款账户。如有疑问请联系客服并提供订单号。
                        </div>
                    </div>
                    
                    <div class="faq-item">
                        <div class="faq-question">
                            <span>支持哪些支付方式？</span>
                            <span class="faq-toggle">+</span>
                        </div>
                        <div class="faq-answer">
                            我们支持支付宝、微信支付、银行卡、PayPal等多种支付方式，满足不同用户需求。
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 订单问题 -->
            <div id="faq-orders" class="faq-category">
                <h3 class="faq-category-title">📦 订单问题</h3>
                <div class="faq-list">
                    <div class="faq-item">
                        <div class="faq-question">
                            <span>如何查看订单状态？</span>
                            <span class="faq-toggle">+</span>
                        </div>
                        <div class="faq-answer">
                            登录账户后，进入"我的订单"页面即可查看所有订单的详细状态和配送信息。
                        </div>
                    </div>
                    
                    <div class="faq-item">
                        <div class="faq-question">
                            <span>可以取消已提交的订单吗？</span>
                            <span class="faq-toggle">+</span>
                        </div>
                        <div class="faq-answer">
                            订单在"待处理"状态时可以取消。一旦开始处理或已完成的订单无法取消，但可以申请退款。
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 游戏相关 -->
            <div id="faq-games" class="faq-category">
                <h3 class="faq-category-title">🎮 游戏相关</h3>
                <div class="faq-list">
                    <div class="faq-item">
                        <div class="faq-question">
                            <span>激活码如何使用？</span>
                            <span class="faq-toggle">+</span>
                        </div>
                        <div class="faq-answer">
                            在Steam客户端点击"游戏"→"在Steam上激活产品"，输入激活码即可。详细教程请参考Steam官方帮助。
                        </div>
                    </div>
                    
                    <div class="faq-item">
                        <div class="faq-question">
                            <span>游戏有区域限制吗？</span>
                            <span class="faq-toggle">+</span>
                        </div>
                        <div class="faq-answer">
                            部分游戏可能有区域限制。购买前请查看商品详情中的区域说明，确保您的账户地区支持该游戏。
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 服务承诺 -->
    <section class="service-promise">
        <div class="container">
            <h2 class="section-title">服务承诺</h2>
            <div class="promise-cards">
                <div class="promise-card">
                    <div class="promise-icon">⚡</div>
                    <h3 class="promise-title">快速响应</h3>
                    <p class="promise-desc">7x24小时在线客服，紧急问题30分钟内响应</p>
                </div>
                
                <div class="promise-card">
                    <div class="promise-icon">🔒</div>
                    <h3 class="promise-title">安全保障</h3>
                    <p class="promise-desc">正版保证，资金安全，隐私保护，值得信赖</p>
                </div>
                
                <div class="promise-card">
                    <div class="promise-icon">😊</div>
                    <h3 class="promise-title">满意服务</h3>
                    <p class="promise-desc">专业团队，用心服务，客户满意度99.5%</p>
                </div>
                
                <div class="promise-card">
                    <div class="promise-icon">💯</div>
                    <h3 class="promise-title">品质承诺</h3>
                    <p class="promise-desc">7年行业经验，累计服务50万+用户</p>
                </div>
            </div>
        </div>
    </section>

    <!-- 页脚 -->
    <footer class="footer">
        <div class="container">
            <p>&copy; 2024 SteamPY. All rights reserved.</p>
        </div>
    </footer>

    <script src="/scripts/main.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            
            // FAQ展开/收起
            const faqItems = document.querySelectorAll('.faq-item');
            
            faqItems.forEach(item => {
                const question = item.querySelector('.faq-question');
                const answer = item.querySelector('.faq-answer');
                const toggle = item.querySelector('.faq-toggle');
                
                question.addEventListener('click', function() {
                    const isOpen = answer.style.display === 'block';
                    
                    // 关闭所有FAQ
                    faqItems.forEach(faq => {
                        faq.querySelector('.faq-answer').style.display = 'none';
                        faq.querySelector('.faq-toggle').textContent = '+';
                    });
                    
                    // 切换当前FAQ
                    if (!isOpen) {
                        answer.style.display = 'block';
                        toggle.textContent = '-';
                    }
                });
            });
            
            // 工单表单提交
            const ticketForm = document.getElementById('ticket-form');
            
            ticketForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const formData = {
                    type: document.getElementById('ticket-type').value,
                    subject: document.getElementById('ticket-subject').value,
                    description: document.getElementById('ticket-description').value,
                    email: document.getElementById('ticket-email').value,
                    phone: document.getElementById('ticket-phone').value,
                    priority: document.getElementById('ticket-priority').value
                };
                
                // 表单验证
                if (!formData.type || !formData.subject || !formData.description || !formData.email) {
                    showMessage('请填写完整的工单信息', 'error');
                    return;
                }
                
                // 模拟提交
                const submitBtn = this.querySelector('button[type="submit"]');
                submitBtn.textContent = '提交中...';
                submitBtn.disabled = true;
                
                setTimeout(() => {
                    showMessage('工单提交成功！工单号：TK' + Date.now(), 'success');
                    this.reset();
                    submitBtn.textContent = '提交工单';
                    submitBtn.disabled = false;
                }, 2000);
            });
            
            // 平滑滚动到FAQ分类
            document.querySelectorAll('a[href^="#faq-"]').forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });
        });
        
        // 打开在线客服
        function openLiveChat() {
            showMessage('正在连接在线客服...', 'info');
            // 这里可以集成实际的在线客服系统
            setTimeout(() => {
                alert('客服对话窗口将在新窗口中打开\n\n欢迎来到SteamPY客服中心！\n请描述您遇到的问题，我们会尽快为您解答。');
            }, 1000);
        }
        
        // 打开邮件表单
        function openEmailForm() {
            const subject = encodeURIComponent('SteamPY - 用户咨询');
            const body = encodeURIComponent('请在此处描述您的问题...\n\n用户信息：\n- 用户名：\n- 订单号（如有）：\n- 问题类型：\n\n问题描述：\n');
            window.open(`mailto:<EMAIL>?subject=${subject}&body=${body}`);
        }
    </script>
</body>
</html> 