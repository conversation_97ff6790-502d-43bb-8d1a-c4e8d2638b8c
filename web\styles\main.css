/* 重置和基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Steam主题色彩 */
:root {
    --steam-blue: #1b2838;
    --steam-lightblue: #2a475e;
    --steam-green: #90ba3c;
    --steam-orange: #ff6600;
    --steam-yellow: #ffc82c;
}

/* 按钮样式 */
.btn {
    display: inline-block;
    padding: 12px 24px;
    text-decoration: none;
    border-radius: 8px;
    font-weight: 600;
    text-align: center;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 16px;
}

.btn-primary {
    background-color: var(--steam-green);
    color: white;
}

.btn-primary:hover {
    background-color: #7da332;
    transform: translateY(-2px);
}

.btn-outline {
    background-color: transparent;
    color: var(--steam-green);
    border: 2px solid var(--steam-green);
}

.btn-outline:hover {
    background-color: var(--steam-green);
    color: white;
}

.btn-white {
    background-color: white;
    color: #e74c3c;
}

.btn-white:hover {
    background-color: #f8f9fa;
    transform: translateY(-2px);
}

.btn-lg {
    padding: 16px 32px;
    font-size: 18px;
}

/* 导航栏 */
.navbar {
    background: var(--steam-blue);
    color: white;
    padding: 15px 0;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.navbar .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-brand h1 {
    font-size: 28px;
    font-weight: bold;
}

.nav-subtitle {
    color: var(--steam-green);
    font-size: 12px;
}

.nav-menu {
    display: flex;
    gap: 30px;
}

.nav-link {
    color: white;
    text-decoration: none;
    padding: 8px 16px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.nav-link:hover, .nav-link.active {
    color: var(--steam-green);
    background-color: rgba(144, 186, 60, 0.1);
}

.nav-actions {
    display: flex;
    gap: 15px;
    align-items: center;
}

/* 英雄区域 */
.hero {
    background: linear-gradient(135deg, var(--steam-blue) 0%, var(--steam-lightblue) 100%);
    color: white;
    padding: 80px 0;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.2);
}

.hero-content {
    position: relative;
    z-index: 1;
}

.hero-title {
    font-size: 4rem;
    font-weight: bold;
    margin-bottom: 20px;
    line-height: 1.2;
}

.text-green {
    color: var(--steam-green);
    display: block;
}

.hero-description {
    font-size: 1.25rem;
    margin-bottom: 40px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    opacity: 0.9;
}

.hero-actions {
    margin-bottom: 60px;
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

.trust-indicators {
    display: flex;
    justify-content: center;
    gap: 60px;
    max-width: 400px;
    margin: 0 auto;
}

.trust-item {
    text-align: center;
}

.trust-number {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.trust-label {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* 区块标题 */
.section-title {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 60px;
    color: #2c3e50;
}

/* 特色服务 */
.features {
    padding: 80px 0;
    background: white;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 40px;
}

.feature-card {
    text-align: center;
    padding: 40px 30px;
    background: white;
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 16px 48px rgba(0,0,0,0.15);
}

.feature-icon {
    font-size: 3.5rem;
    margin-bottom: 20px;
}

.feature-title {
    font-size: 1.4rem;
    margin-bottom: 15px;
    color: #2c3e50;
}

.feature-description {
    color: #6c757d;
    margin-bottom: 20px;
    line-height: 1.6;
}

.feature-highlight {
    display: inline-block;
    background: var(--steam-green);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
}

/* 限时特惠 */
.flash-sale {
    padding: 80px 0;
    background: linear-gradient(45deg, #e74c3c, #c0392b);
    color: white;
}

.sale-header {
    text-align: center;
    margin-bottom: 60px;
}

.sale-title {
    font-size: 3rem;
    margin-bottom: 15px;
}

.sale-subtitle {
    font-size: 1.2rem;
    margin-bottom: 30px;
    opacity: 0.9;
}

.countdown {
    display: inline-block;
    background: rgba(255,255,255,0.2);
    padding: 15px 30px;
    border-radius: 8px;
    font-size: 1.5rem;
    font-weight: bold;
}

/* 产品网格 */
.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
    margin-bottom: 60px;
}

.product-card {
    background: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    color: #333;
}

.product-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 48px rgba(0,0,0,0.2);
}

.product-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.product-card:hover .product-image img {
    transform: scale(1.1);
}

.discount-badge {
    position: absolute;
    top: 12px;
    left: 12px;
    background: linear-gradient(45deg, #e74c3c, #c0392b);
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: bold;
}

.category-tag {
    position: absolute;
    top: 12px;
    right: 12px;
    background: rgba(27, 40, 56, 0.9);
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
}

.product-info {
    padding: 24px;
}

.product-name {
    font-size: 1.2rem;
    font-weight: bold;
    margin-bottom: 12px;
    color: #2c3e50;
}

.product-tags {
    display: flex;
    gap: 8px;
    margin-bottom: 16px;
    flex-wrap: wrap;
}

.tag {
    background: linear-gradient(45deg, rgba(144, 186, 60, 0.2), rgba(144, 186, 60, 0.1));
    color: var(--steam-green);
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 0.8rem;
    border: 1px solid rgba(144, 186, 60, 0.3);
}

.product-price {
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.current-price {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--steam-green);
}

.original-price {
    font-size: 1rem;
    color: #6c757d;
    text-decoration: line-through;
}

.product-features {
    display: flex;
    justify-content: space-between;
    font-size: 0.9rem;
    color: #6c757d;
}

.sale-action {
    text-align: center;
}

/* 产品分类 */
.categories {
    padding: 80px 0;
    background: #f8f9fa;
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.category-card {
    background: white;
    padding: 40px 30px;
    border-radius: 16px;
    text-align: center;
    text-decoration: none;
    color: inherit;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.category-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 16px 48px rgba(0,0,0,0.15);
    color: inherit;
    text-decoration: none;
}

.category-icon {
    font-size: 3rem;
    margin-bottom: 20px;
}

.category-title {
    font-size: 1.3rem;
    font-weight: bold;
    margin-bottom: 10px;
    color: #2c3e50;
}

.category-description {
    color: #6c757d;
    margin-bottom: 15px;
}

.category-count {
    color: var(--steam-green);
    font-weight: 600;
}

/* 用户评价 */
.testimonials {
    padding: 80px 0;
    background: white;
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-bottom: 60px;
}

.testimonial-card {
    background: #f8f9fa;
    padding: 30px;
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
}

.testimonial-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.testimonial-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    margin-right: 15px;
}

.testimonial-name {
    font-weight: 600;
    margin-bottom: 5px;
}

.testimonial-rating {
    color: #f39c12;
}

.testimonial-content {
    font-style: italic;
    color: #555;
    line-height: 1.6;
}

.rating-summary {
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
}

.rating-stars {
    font-size: 2rem;
}

.rating-score {
    font-size: 1.5rem;
    font-weight: bold;
    color: #2c3e50;
}

.rating-desc {
    color: #6c757d;
}

/* 合作伙伴 */
.partners {
    padding: 40px 0;
    background: #f8f9fa;
}

.partners-title {
    text-align: center;
    margin-bottom: 30px;
    color: #6c757d;
    font-size: 1.2rem;
}

.partners-list {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 60px;
    flex-wrap: wrap;
}

.partner-item {
    font-size: 1.5rem;
    font-weight: bold;
    color: #6c757d;
    opacity: 0.6;
}

/* CTA区域 */
.cta {
    padding: 80px 0;
    background: var(--steam-blue);
    color: white;
    text-align: center;
}

.cta-title {
    font-size: 2.5rem;
    margin-bottom: 20px;
}

.cta-description {
    font-size: 1.2rem;
    margin-bottom: 40px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    opacity: 0.9;
}

.cta-actions {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

/* 页脚 */
.footer {
    background: var(--steam-lightblue);
    color: white;
    text-align: center;
    padding: 30px 0;
}

/* 页面标题 */
.page-header {
    background: linear-gradient(135deg, var(--steam-blue) 0%, var(--steam-lightblue) 100%);
    color: white;
    padding: 60px 0;
    text-align: center;
}

.page-title {
    font-size: 3rem;
    margin-bottom: 15px;
}

.page-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
}

/* 搜索和筛选 */
.filters {
    background: white;
    padding: 30px 0;
    border-bottom: 1px solid #eee;
}

.search-bar {
    display: flex;
    margin-bottom: 30px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.search-input {
    flex: 1;
    padding: 15px 20px;
    border: 2px solid #ddd;
    border-radius: 8px 0 0 8px;
    font-size: 16px;
    outline: none;
}

.search-input:focus {
    border-color: var(--steam-green);
}

.search-btn {
    padding: 15px 20px;
    background: var(--steam-green);
    color: white;
    border: none;
    border-radius: 0 8px 8px 0;
    cursor: pointer;
    font-size: 16px;
}

.filter-options {
    display: flex;
    gap: 30px;
    justify-content: center;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 10px;
}

.filter-group label {
    font-weight: 600;
}

.filter-group select {
    padding: 8px 15px;
    border: 2px solid #ddd;
    border-radius: 5px;
    font-size: 14px;
}

/* 产品区域 */
.products-section {
    padding: 60px 0;
    background: #f8f9fa;
}

.load-more {
    text-align: center;
    margin-top: 60px;
}

/* 钱包充值页面 */
.wallet-section {
    padding: 60px 0;
    background: #f8f9fa;
}

.wallet-form-container {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 40px;
    max-width: 1200px;
    margin: 0 auto;
}

.wallet-form {
    background: white;
    padding: 40px;
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
}

.form-title {
    font-size: 1.8rem;
    margin-bottom: 30px;
    text-align: center;
    color: #2c3e50;
}

/* 金额选择 */
.amount-selection {
    margin-bottom: 40px;
}

.amount-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
    margin-bottom: 20px;
}

.amount-card {
    background: white;
    border: 2px solid #ddd;
    border-radius: 12px;
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.amount-card:hover {
    border-color: var(--steam-green);
    transform: translateY(-2px);
}

.amount-card.selected {
    border-color: var(--steam-green);
    background: rgba(144, 186, 60, 0.1);
}

.amount-card.popular {
    border-color: var(--steam-green);
    background: linear-gradient(45deg, rgba(144, 186, 60, 0.1), rgba(144, 186, 60, 0.05));
}

.popular-badge {
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--steam-green);
    color: white;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: bold;
}

.amount-value {
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 5px;
    color: #2c3e50;
}

.amount-bonus {
    font-size: 0.9rem;
    color: var(--steam-green);
    font-weight: 600;
}

.custom-amount {
    margin-top: 20px;
}

.custom-amount label {
    display: block;
    margin-bottom: 10px;
    font-weight: 600;
}

.custom-amount input {
    width: 100%;
    padding: 15px;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-size: 16px;
}

/* 表单组 */
.form-group {
    margin-bottom: 30px;
}

.form-group label {
    display: block;
    margin-bottom: 10px;
    font-weight: 600;
}

.form-group input {
    width: 100%;
    padding: 15px;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-size: 16px;
}

.form-group input:focus {
    border-color: var(--steam-green);
    outline: none;
}

.help-text {
    font-size: 0.9rem;
    color: #6c757d;
    margin-top: 5px;
}

/* 支付方式 */
.payment-methods {
    margin-bottom: 40px;
}

.payment-methods h3 {
    margin-bottom: 20px;
    font-size: 1.2rem;
}

.payment-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
}

.payment-option {
    background: white;
    border: 2px solid #ddd;
    border-radius: 12px;
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.payment-option:hover {
    border-color: var(--steam-green);
    transform: translateY(-2px);
}

.payment-option.selected {
    border-color: var(--steam-green);
    background: rgba(144, 186, 60, 0.1);
}

.payment-icon {
    font-size: 2rem;
    margin-bottom: 10px;
}

.payment-name {
    font-weight: bold;
    margin-bottom: 5px;
}

.payment-desc {
    font-size: 0.9rem;
    color: #6c757d;
}

/* 充值按钮 */
.recharge-btn {
    width: 100%;
    margin-top: 20px;
}

.recharge-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
}

/* 订单摘要 */
.order-summary {
    background: white;
    padding: 30px;
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    height: fit-content;
}

.order-summary h3 {
    font-size: 1.4rem;
    margin-bottom: 25px;
    text-align: center;
    color: #2c3e50;
}

.summary-content {
    margin-bottom: 30px;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
    padding: 8px 0;
}

.summary-item.total {
    font-weight: bold;
    font-size: 1.1rem;
}

.summary-divider {
    border-top: 1px solid #eee;
    margin: 20px 0;
}

.free {
    color: var(--steam-green);
    font-weight: bold;
}

.highlight {
    color: var(--steam-green);
    font-weight: bold;
    font-size: 1.2rem;
}

/* 优惠券 */
.coupon-section h4 {
    margin-bottom: 15px;
    font-size: 1.1rem;
}

.coupon-input {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.coupon-input input {
    flex: 1;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
}

.coupon-apply-btn {
    padding: 10px 15px;
    font-size: 14px;
}

.available-coupons {
    margin-top: 15px;
}

.coupon-item {
    background: linear-gradient(45deg, rgba(144, 186, 60, 0.1), rgba(144, 186, 60, 0.05));
    border: 1px solid rgba(144, 186, 60, 0.3);
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.coupon-item:hover {
    background: rgba(144, 186, 60, 0.2);
}

.coupon-name {
    font-weight: bold;
    margin-bottom: 5px;
}

.coupon-value {
    color: var(--steam-green);
    font-weight: bold;
}

/* 优势区域 */
.advantages {
    padding: 80px 0;
    background: white;
}

.advantages-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
}

.advantage-card {
    text-align: center;
    padding: 30px 20px;
}

.advantage-icon {
    font-size: 3rem;
    margin-bottom: 20px;
}

.advantage-card h3 {
    font-size: 1.3rem;
    margin-bottom: 15px;
    color: #2c3e50;
}

.advantage-card p {
    color: #6c757d;
    line-height: 1.6;
}

/* FAQ */
.faq {
    padding: 80px 0;
    background: #f8f9fa;
}

.faq-list {
    max-width: 800px;
    margin: 0 auto;
}

.faq-item {
    background: white;
    border-radius: 8px;
    margin-bottom: 15px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.faq-question {
    padding: 20px;
    background: white;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 600;
    transition: background 0.3s ease;
}

.faq-question:hover {
    background: #f8f9fa;
}

.faq-toggle {
    font-size: 1.5rem;
    color: var(--steam-green);
    font-weight: bold;
}

.faq-answer {
    padding: 0 20px 20px;
    display: none;
    color: #6c757d;
    line-height: 1.6;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }
    
    .nav-menu {
        display: none;
    }
    
    .hero-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .trust-indicators {
        gap: 30px;
    }
    
    .cta-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .products-grid {
        grid-template-columns: 1fr;
    }
    
    .categories-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .wallet-form-container {
        grid-template-columns: 1fr;
    }
    
    .amount-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .payment-grid {
        grid-template-columns: 1fr;
    }
    
    .filter-options {
        flex-direction: column;
        align-items: center;
    }
}

/* 个人中心、订单管理和客服支持页面样式 */
.profile-section, .orders-section {
    min-height: calc(100vh - 160px);
    padding: 2rem 0;
    background: #f8f9fa;
}

.profile-layout {
    display: grid;
    grid-template-columns: 280px 1fr;
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.profile-sidebar {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    height: fit-content;
    box-shadow: 0 2px 12px rgba(0,0,0,0.1);
}

.user-info {
    text-align: center;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid #e9ecef;
    margin-bottom: 1.5rem;
}

.user-avatar {
    width: 80px;
    height: 80px;
    margin: 0 auto 1rem;
    border-radius: 50%;
    overflow: hidden;
    border: 3px solid var(--steam-blue);
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.user-name {
    font-size: 1.2rem;
    font-weight: bold;
    color: #333;
    margin-bottom: 0.5rem;
}

.user-level {
    color: var(--steam-green);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.user-join {
    color: #6c757d;
    font-size: 0.9rem;
}

.profile-nav {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.profile-nav-link {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    color: #6c757d;
    text-decoration: none;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.profile-nav-link:hover {
    background: #f8f9fa;
    color: var(--steam-blue);
}

.profile-nav-link.active {
    background: var(--steam-blue);
    color: white;
}

.profile-content {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 2px 12px rgba(0,0,0,0.1);
}

.profile-tab {
    display: none;
}

.profile-tab.active {
    display: block;
}

.tab-title {
    font-size: 1.5rem;
    font-weight: bold;
    color: #333;
    margin-bottom: 1.5rem;
    padding-bottom: 0.75rem;
    border-bottom: 2px solid var(--steam-blue);
}

.dashboard-stats, .orders-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.dashboard-stats .stat-card {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    background: linear-gradient(135deg, var(--steam-blue) 0%, #4a90a4 100%);
    border-radius: 12px;
    color: white;
}

.dashboard-stats .stat-icon {
    font-size: 2rem;
}

.dashboard-stats .stat-number {
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 0.25rem;
}

.dashboard-stats .stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* 订单页面样式 */
.orders-filter {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 12px rgba(0,0,0,0.1);
}

.filter-tabs {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
}

.filter-tab {
    padding: 0.5rem 1rem;
    background: #f8f9fa;
    border: none;
    border-radius: 20px;
    color: #6c757d;
    cursor: pointer;
    transition: all 0.3s ease;
}

.filter-tab.active,
.filter-tab:hover {
    background: var(--steam-blue);
    color: white;
}

.filter-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.filter-group label {
    font-weight: 600;
    color: #333;
    font-size: 0.9rem;
}

.filter-group select,
.filter-group input {
    padding: 0.5rem;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    background: white;
}

.orders-list {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.order-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.1);
    overflow: hidden;
}

.order-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.order-id {
    font-weight: bold;
    color: #333;
}

.order-date {
    color: #6c757d;
    font-size: 0.9rem;
}

.order-status {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.status-completed {
    background: #d4edda;
    color: #155724;
}

.status-processing {
    background: #d1ecf1;
    color: #0c5460;
}

.status-pending {
    background: #fff3cd;
    color: #856404;
}

.status-failed {
    background: #f8d7da;
    color: #721c24;
}

.order-body {
    padding: 1.5rem;
}

.order-items {
    margin-bottom: 1rem;
}

.order-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
}

.item-image {
    width: 60px;
    height: 60px;
    border-radius: 8px;
    object-fit: cover;
}

.item-details {
    flex: 1;
}

.item-name {
    font-weight: 600;
    color: #333;
    margin-bottom: 0.25rem;
}

.item-desc {
    font-size: 0.9rem;
    color: #6c757d;
}

.item-price {
    font-weight: bold;
    color: var(--steam-green);
    font-size: 1.1rem;
}

.delivery-info {
    background: #e8f5e8;
    border: 1px solid #c3e6cb;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
}

.delivery-title {
    font-weight: bold;
    color: #155724;
    margin-bottom: 0.5rem;
}

.delivery-code {
    font-family: monospace;
    background: #fff;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    border: 1px solid #ccc;
}

.copy-btn {
    background: var(--steam-green);
    color: white;
    border: none;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8rem;
    margin-left: 0.5rem;
}

.order-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 1rem;
    border-top: 1px solid #e9ecef;
}

.order-total {
    font-weight: bold;
    font-size: 1.2rem;
    color: #333;
}

.order-actions {
    display: flex;
    gap: 0.5rem;
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
}

.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.1);
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.empty-state h3 {
    color: #333;
    margin-bottom: 0.5rem;
}

.empty-state p {
    color: #6c757d;
    margin-bottom: 1.5rem;
}

/* 客服支持页面样式 */
.quick-help {
    padding: 4rem 0;
    background: white;
}

.help-categories {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.help-category {
    text-align: center;
    padding: 2rem;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    transition: all 0.3s ease;
}

.help-category:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
}

.category-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.category-title {
    font-size: 1.2rem;
    font-weight: bold;
    color: #333;
    margin-bottom: 0.5rem;
}

.category-desc {
    color: #6c757d;
    margin-bottom: 1.5rem;
}

.contact-support {
    padding: 4rem 0;
    background: linear-gradient(135deg, var(--steam-blue) 0%, #2a475e 100%);
    color: white;
}

.contact-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.contact-option {
    background: rgba(255,255,255,0.1);
    padding: 2rem;
    border-radius: 12px;
    text-align: center;
    backdrop-filter: blur(10px);
}

.contact-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.contact-title {
    font-size: 1.2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.contact-desc {
    opacity: 0.9;
    margin-bottom: 1rem;
}

.contact-status {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    margin-bottom: 1rem;
    display: inline-block;
}

.contact-status.online {
    background: var(--steam-green);
}

.contact-info {
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.contact-time {
    font-size: 0.9rem;
    opacity: 0.8;
    margin-bottom: 1rem;
}

.contact-btn {
    width: 100%;
}

.ticket-system {
    padding: 4rem 0;
    background: #f8f9fa;
}

.ticket-form-container {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 3rem;
    max-width: 1000px;
    margin: 0 auto;
}

.ticket-form {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.1);
}

.ticket-info {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.1);
    height: fit-content;
}

.faq-section {
    padding: 4rem 0;
    background: white;
}

.faq-category {
    margin-bottom: 3rem;
}

.faq-category-title {
    font-size: 1.3rem;
    font-weight: bold;
    color: #333;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
}

.faq-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.faq-item {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    overflow: hidden;
}

.faq-question {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: white;
    cursor: pointer;
    transition: background 0.3s ease;
}

.faq-question:hover {
    background: #f8f9fa;
}

.faq-toggle {
    font-weight: bold;
    font-size: 1.2rem;
}

.faq-answer {
    padding: 1rem;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
    display: none;
}

.service-promise {
    padding: 4rem 0;
    background: #f8f9fa;
}

.promise-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.promise-card {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 2px 12px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.promise-card:hover {
    transform: translateY(-5px);
}

.promise-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.promise-title {
    font-size: 1.2rem;
    font-weight: bold;
    color: #333;
    margin-bottom: 0.5rem;
}

.promise-desc {
    color: #6c757d;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .profile-layout {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .dashboard-stats, .orders-stats {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .filter-row {
        grid-template-columns: 1fr;
    }
    
    .order-footer {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .order-actions {
        justify-content: center;
    }
    
    .help-categories, .contact-options, .promise-cards {
        grid-template-columns: 1fr;
    }
    
    .ticket-form-container {
        grid-template-columns: 1fr;
    }
}

/* 新导航栏组件样式 */
.user-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-balance {
    background: var(--steam-green);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
}

.user-dropdown {
    position: relative;
}

.user-avatar {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: none;
    border: none;
    color: #333;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 8px;
    transition: background 0.3s ease;
}

.user-avatar:hover {
    background: #f8f9fa;
}

.user-avatar img {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    object-fit: cover;
}

.user-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    border-radius: 12px;
    padding: 0.5rem 0;
    min-width: 200px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 1000;
}

.user-menu.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.user-menu-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    color: #333;
    text-decoration: none;
    transition: background 0.3s ease;
    border: none;
    background: none;
    width: 100%;
    text-align: left;
    cursor: pointer;
}

.user-menu-item:hover {
    background: #f8f9fa;
    color: var(--steam-blue);
}

.user-menu-item.logout-btn {
    color: #dc3545;
}

.user-menu-item.logout-btn:hover {
    background: #fff5f5;
    color: #dc3545;
}

.user-menu-divider {
    height: 1px;
    background: #e9ecef;
    margin: 0.5rem 0;
}

/* 移动端菜单 */
.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    gap: 4px;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.5rem;
}

.mobile-menu-toggle span {
    width: 25px;
    height: 3px;
    background: #333;
    border-radius: 2px;
    transition: all 0.3s ease;
}

.mobile-menu {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    border-radius: 0 0 12px 12px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-20px);
    transition: all 0.3s ease;
    z-index: 999;
}

.mobile-menu.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.mobile-menu-content {
    padding: 1rem;
}

.mobile-nav-link {
    display: block;
    padding: 0.75rem 0;
    color: #333;
    text-decoration: none;
    border-bottom: 1px solid #f0f0f0;
    transition: color 0.3s ease;
}

.mobile-nav-link:last-child {
    border-bottom: none;
}

.mobile-nav-link:hover,
.mobile-nav-link.active {
    color: var(--steam-blue);
    font-weight: 600;
}

.mobile-nav-divider {
    height: 1px;
    background: #e9ecef;
    margin: 0.5rem 0;
}

.mobile-user-info {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #e9ecef;
}

.mobile-user-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.mobile-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.mobile-username {
    font-weight: 600;
    color: #333;
}

.mobile-balance {
    font-size: 0.9rem;
    color: var(--steam-green);
    font-weight: 600;
}

.mobile-nav-link.logout-btn {
    color: #dc3545;
    background: none;
    border: none;
    width: 100%;
    text-align: left;
    cursor: pointer;
}

@media (max-width: 768px) {
    .nav-menu,
    .nav-actions {
        display: none;
    }
    
    .mobile-menu-toggle {
        display: flex;
    }
    
    .mobile-menu {
        display: block;
    }
} 