from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.models.user import User
from app.models.product import Order
from app.api.v1.endpoints.auth import get_current_user
from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime
from decimal import Decimal

router = APIRouter()


class OrderResponse(BaseModel):
    id: int
    order_number: str
    status: str
    product_name: str
    product_type: str
    total_amount: float
    final_amount: float
    created_at: datetime
    completed_at: Optional[datetime] = None
    delivery_info: Optional[str] = None
    
    class Config:
        from_attributes = True


@router.get("/", response_model=List[OrderResponse])
async def get_orders(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取当前用户的订单列表"""
    try:
        orders = db.query(Order).filter(
            Order.buyer_id == current_user.id
        ).order_by(Order.created_at.desc()).all()
        
        # 转换为响应格式
        order_list = []
        for order in orders:
            order_data = OrderResponse(
                id=order.id,
                order_number=order.order_number,
                status=order.status.value if hasattr(order.status, 'value') else str(order.status),
                product_name=order.product_name,
                product_type=order.product_type.value if hasattr(order.product_type, 'value') else str(order.product_type),
                total_amount=float(order.total_amount),
                final_amount=float(order.final_amount),
                created_at=order.created_at,
                completed_at=order.completed_at,
                delivery_info=order.delivery_info
            )
            order_list.append(order_data)
        
        return order_list
        
    except Exception as e:
        # 如果出错，返回空列表而不是错误
        print(f"获取订单失败: {e}")
        return []


@router.get("/{order_id}", response_model=OrderResponse)
async def get_order(
    order_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取订单详情"""
    order = db.query(Order).filter(
        Order.id == order_id,
        Order.buyer_id == current_user.id
    ).first()
    
    if not order:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Order not found"
        )
    
    return OrderResponse(
        id=order.id,
        order_number=order.order_number,
        status=order.status.value if hasattr(order.status, 'value') else str(order.status),
        product_name=order.product_name,
        product_type=order.product_type.value if hasattr(order.product_type, 'value') else str(order.product_type),
        total_amount=float(order.total_amount),
        final_amount=float(order.final_amount),
        created_at=order.created_at,
        completed_at=order.completed_at,
        delivery_info=order.delivery_info
    ) 