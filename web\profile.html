<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人中心 - SteamPY</title>
    <link rel="stylesheet" href="/static/main.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="container">
            <div class="nav-brand">
                <h1>SteamPY</h1>
                <span class="nav-subtitle">Steam游戏交易平台</span>
            </div>
            
            <div class="nav-menu">
                <a href="index.html" class="nav-link">首页</a>
                <a href="products.html" class="nav-link">产品商城</a>
                <a href="wallet.html" class="nav-link">Steam钱包</a>
                <a href="orders.html" class="nav-link">我的订单</a>
                <a href="support.html" class="nav-link">客服支持</a>
            </div>
            
            <div class="nav-actions">
                <a href="profile.html" class="nav-link active">个人中心</a>
                <a href="login.html" class="btn btn-primary">登录</a>
                <a href="register.html" class="btn btn-outline">注册</a>
            </div>
        </div>
    </nav>

    <!-- 个人中心内容 -->
    <section class="profile-section">
        <div class="container">
            <div class="profile-layout">
                <!-- 侧边菜单 -->
                <div class="profile-sidebar">
                    <div class="user-info">
                        <div class="user-avatar">
                            <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=80&h=80&fit=crop&crop=face" alt="用户头像" id="user-avatar">
                        </div>
                        <div class="user-details">
                            <h3 class="user-name" id="user-name">游戏玩家001</h3>
                            <p class="user-level">VIP会员</p>
                            <p class="user-join">加入时间：2023-01-15</p>
                        </div>
                    </div>
                    
                    <nav class="profile-nav">
                        <a href="#dashboard" class="profile-nav-link active" data-tab="dashboard">
                            <i class="fas fa-tachometer-alt"></i> 概览
                        </a>
                        <a href="#personal" class="profile-nav-link" data-tab="personal">
                            <i class="fas fa-user"></i> 个人信息
                        </a>
                        <a href="#security" class="profile-nav-link" data-tab="security">
                            <i class="fas fa-shield-alt"></i> 安全设置
                        </a>
                        <a href="#preferences" class="profile-nav-link" data-tab="preferences">
                            <i class="fas fa-cog"></i> 偏好设置
                        </a>
                        <a href="#history" class="profile-nav-link" data-tab="history">
                            <i class="fas fa-history"></i> 交易记录
                        </a>
                        <a href="#rewards" class="profile-nav-link" data-tab="rewards">
                            <i class="fas fa-gift"></i> 积分奖励
                        </a>
                    </nav>
                </div>

                <!-- 主要内容区 -->
                <div class="profile-content">
                    <!-- 概览面板 -->
                    <div id="dashboard" class="profile-tab active">
                        <h2 class="tab-title">账户概览</h2>
                        
                        <div class="dashboard-stats">
                            <div class="stat-card">
                                <div class="stat-icon">💰</div>
                                <div class="stat-content">
                                    <div class="stat-number">¥1,280</div>
                                    <div class="stat-label">累计消费</div>
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-icon">📦</div>
                                <div class="stat-content">
                                    <div class="stat-number">12</div>
                                    <div class="stat-label">总订单数</div>
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-icon">🎮</div>
                                <div class="stat-content">
                                    <div class="stat-number">8</div>
                                    <div class="stat-label">拥有游戏</div>
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-icon">⭐</div>
                                <div class="stat-content">
                                    <div class="stat-number">2,350</div>
                                    <div class="stat-label">积分余额</div>
                                </div>
                            </div>
                        </div>

                        <div class="recent-activity">
                            <h3>近期活动</h3>
                            <div class="activity-list">
                                <div class="activity-item">
                                    <div class="activity-icon">💳</div>
                                    <div class="activity-content">
                                        <div class="activity-title">Steam钱包充值 ¥100</div>
                                        <div class="activity-time">2024-12-24 14:30</div>
                                    </div>
                                    <div class="activity-status success">已完成</div>
                                </div>
                                <div class="activity-item">
                                    <div class="activity-icon">🎮</div>
                                    <div class="activity-content">
                                        <div class="activity-title">购买 Cyberpunk 2077</div>
                                        <div class="activity-time">2024-12-23 20:15</div>
                                    </div>
                                    <div class="activity-status success">已完成</div>
                                </div>
                                <div class="activity-item">
                                    <div class="activity-icon">⭐</div>
                                    <div class="activity-content">
                                        <div class="activity-title">获得积分奖励 +100</div>
                                        <div class="activity-time">2024-12-22 16:45</div>
                                    </div>
                                    <div class="activity-status">积分</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 个人信息面板 -->
                    <div id="personal" class="profile-tab">
                        <h2 class="tab-title">个人信息</h2>
                        
                        <form class="profile-form">
                            <div class="form-section">
                                <h3>基本信息</h3>
                                <div class="form-grid">
                                    <div class="form-group">
                                        <label>用户名</label>
                                        <input type="text" name="username" disabled>
                                    </div>
                                    <div class="form-group">
                                        <label>真实姓名</label>
                                        <input type="text" name="nickname" placeholder="请输入真实姓名">
                                    </div>
                                    <div class="form-group">
                                        <label>邮箱地址</label>
                                        <input type="email" name="email" placeholder="请输入邮箱">
                                    </div>
                                    <div class="form-group">
                                        <label>手机号码</label>
                                        <input type="tel" name="phone" placeholder="请输入手机号">
                                    </div>
                                </div>
                            </div>
                            
                            <div class="form-section">
                                <h3>Steam信息</h3>
                                <div class="form-grid">
                                    <div class="form-group">
                                        <label>Steam ID</label>
                                        <input type="text" name="steam_id" placeholder="请输入Steam ID">
                                    </div>
                                    <div class="form-group">
                                        <label>Steam用户名</label>
                                        <input type="text" name="steam_username" disabled>
                                    </div>
                                </div>
                            </div>
                            
                            <button type="submit" class="btn btn-primary">保存更改</button>
                        </form>
                    </div>

                    <!-- 安全设置面板 -->
                    <div id="security" class="profile-tab">
                        <h2 class="tab-title">安全设置</h2>
                        
                        <div class="security-section">
                            <h3>密码管理</h3>
                            <form class="password-form">
                                <div class="form-group">
                                    <label>当前密码</label>
                                    <input type="password" placeholder="请输入当前密码">
                                </div>
                                <div class="form-group">
                                    <label>新密码</label>
                                    <input type="password" placeholder="请输入新密码">
                                </div>
                                <div class="form-group">
                                    <label>确认新密码</label>
                                    <input type="password" placeholder="再次输入新密码">
                                </div>
                                <button type="submit" class="btn btn-primary">修改密码</button>
                            </form>
                        </div>
                        
                        <div class="security-section">
                            <h3>两步验证</h3>
                            <div class="security-item">
                                <div class="security-info">
                                    <div class="security-title">手机验证</div>
                                    <div class="security-desc">通过手机短信验证码增强账户安全</div>
                                </div>
                                <button class="btn btn-outline security-toggle">启用</button>
                            </div>
                            <div class="security-item">
                                <div class="security-info">
                                    <div class="security-title">邮箱验证</div>
                                    <div class="security-desc">通过邮箱验证码确认重要操作</div>
                                </div>
                                <button class="btn btn-primary security-toggle">已启用</button>
                            </div>
                        </div>
                        
                        <div class="security-section">
                            <h3>登录记录</h3>
                            <div class="login-history">
                                <div class="login-item">
                                    <div class="login-info">
                                        <div class="login-time">2024-12-24 14:30</div>
                                        <div class="login-location">北京市 - Chrome浏览器</div>
                                    </div>
                                    <div class="login-status current">当前会话</div>
                                </div>
                                <div class="login-item">
                                    <div class="login-info">
                                        <div class="login-time">2024-12-23 20:15</div>
                                        <div class="login-location">上海市 - Firefox浏览器</div>
                                    </div>
                                    <div class="login-status">已退出</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 偏好设置面板 -->
                    <div id="preferences" class="profile-tab">
                        <h2 class="tab-title">偏好设置</h2>
                        
                        <div class="preferences-section">
                            <h3>通知设置</h3>
                            <div class="preference-item">
                                <div class="preference-info">
                                    <div class="preference-title">邮件通知</div>
                                    <div class="preference-desc">接收订单状态、优惠活动等邮件通知</div>
                                </div>
                                <label class="switch">
                                    <input type="checkbox" checked>
                                    <span class="slider"></span>
                                </label>
                            </div>
                            <div class="preference-item">
                                <div class="preference-info">
                                    <div class="preference-title">短信通知</div>
                                    <div class="preference-desc">接收重要交易和安全提醒短信</div>
                                </div>
                                <label class="switch">
                                    <input type="checkbox">
                                    <span class="slider"></span>
                                </label>
                            </div>
                        </div>
                        
                        <div class="preferences-section">
                            <h3>语言和地区</h3>
                            <div class="form-group">
                                <label>显示语言</label>
                                <select class="form-input">
                                    <option value="zh-CN">简体中文</option>
                                    <option value="en-US">English</option>
                                    <option value="ja-JP">日本語</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>货币单位</label>
                                <select class="form-input">
                                    <option value="CNY">人民币 (¥)</option>
                                    <option value="USD">美元 ($)</option>
                                    <option value="EUR">欧元 (€)</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- 交易记录面板 -->
                    <div id="history" class="profile-tab">
                        <h2 class="tab-title">交易记录</h2>
                        
                        <div class="history-filters">
                            <select class="filter-select">
                                <option value="">全部类型</option>
                                <option value="recharge">充值</option>
                                <option value="purchase">购买</option>
                                <option value="refund">退款</option>
                            </select>
                            <select class="filter-select">
                                <option value="">全部时间</option>
                                <option value="7">最近7天</option>
                                <option value="30">最近30天</option>
                                <option value="90">最近3个月</option>
                            </select>
                        </div>
                        
                        <div class="history-list">
                            <div class="history-item">
                                <div class="history-date">2024-12-24</div>
                                <div class="history-content">
                                    <div class="history-title">Steam钱包充值</div>
                                    <div class="history-desc">充值金额：¥100</div>
                                </div>
                                <div class="history-amount success">+¥95.00</div>
                            </div>
                            <div class="history-item">
                                <div class="history-date">2024-12-23</div>
                                <div class="history-content">
                                    <div class="history-title">购买游戏</div>
                                    <div class="history-desc">Cyberpunk 2077</div>
                                </div>
                                <div class="history-amount expense">-¥198.00</div>
                            </div>
                        </div>
                    </div>

                    <!-- 积分奖励面板 -->
                    <div id="rewards" class="profile-tab">
                        <h2 class="tab-title">积分奖励</h2>
                        
                        <div class="rewards-summary">
                            <div class="rewards-balance">
                                <div class="balance-title">当前积分</div>
                                <div class="balance-amount">2,350</div>
                                <div class="balance-desc">可兑换 ¥23.50 优惠券</div>
                            </div>
                        </div>
                        
                        <div class="rewards-options">
                            <h3>积分兑换</h3>
                            <div class="reward-items">
                                <div class="reward-item">
                                    <div class="reward-icon">🎁</div>
                                    <div class="reward-content">
                                        <div class="reward-title">¥5优惠券</div>
                                        <div class="reward-cost">500积分</div>
                                    </div>
                                    <button class="btn btn-outline">兑换</button>
                                </div>
                                <div class="reward-item">
                                    <div class="reward-icon">🎁</div>
                                    <div class="reward-content">
                                        <div class="reward-title">¥10优惠券</div>
                                        <div class="reward-cost">1000积分</div>
                                    </div>
                                    <button class="btn btn-outline">兑换</button>
                                </div>
                                <div class="reward-item">
                                    <div class="reward-icon">🎁</div>
                                    <div class="reward-content">
                                        <div class="reward-title">¥20优惠券</div>
                                        <div class="reward-cost">2000积分</div>
                                    </div>
                                    <button class="btn btn-primary">兑换</button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="rewards-history">
                            <h3>积分记录</h3>
                            <div class="points-list">
                                <div class="points-item">
                                    <div class="points-content">
                                        <div class="points-title">购买商品获得积分</div>
                                        <div class="points-time">2024-12-24</div>
                                    </div>
                                    <div class="points-amount earn">+100</div>
                                </div>
                                <div class="points-item">
                                    <div class="points-content">
                                        <div class="points-title">兑换优惠券</div>
                                        <div class="points-time">2024-12-20</div>
                                    </div>
                                    <div class="points-amount spend">-500</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 页脚 -->
    <footer class="footer">
        <div class="container">
            <p>&copy; 2024 SteamPY. All rights reserved.</p>
        </div>
    </footer>

    <script src="/scripts/main.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', async function() {
            
            // 标签页切换
            const navLinks = document.querySelectorAll('.profile-nav-link');
            const tabs = document.querySelectorAll('.profile-tab');
            
            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    const targetTab = this.dataset.tab;
                    
                    // 更新导航状态
                    navLinks.forEach(nav => nav.classList.remove('active'));
                    this.classList.add('active');
                    
                    // 切换标签页
                    tabs.forEach(tab => tab.classList.remove('active'));
                    document.getElementById(targetTab).classList.add('active');
                });
            });
            
            // 检查登录状态和获取用户数据
            const token = localStorage.getItem('access_token');
            if (!token) {
                showNotLoggedInState();
                return;
            }
            
            try {
                // 获取用户信息
                const userResponse = await fetch('/api/v1/auth/profile', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                if (!userResponse.ok) {
                    throw new Error('获取用户信息失败');
                }
                
                const userData = await userResponse.json();
                console.log('用户数据:', userData);
                
                // 获取用户订单数据
                const ordersResponse = await fetch('/api/v1/orders/', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                let ordersData = [];
                if (ordersResponse.ok) {
                    const ordersResult = await ordersResponse.json();
                    // 检查返回的数据格式，确保是数组
                    if (Array.isArray(ordersResult)) {
                        ordersData = ordersResult;
                    } else if (ordersResult.data && Array.isArray(ordersResult.data)) {
                        ordersData = ordersResult.data;
                    } else {
                        // 如果API还未实现，使用空数组
                        console.log('订单API返回:', ordersResult);
                        ordersData = [];
                    }
                } else {
                    console.log('获取订单数据失败:', ordersResponse.status);
                }
                
                // 填充用户数据到页面
                populateUserData(userData, ordersData);
                
            } catch (error) {
                console.error('加载用户数据失败:', error);
                showMessage('加载用户数据失败，请重新登录', 'error');
                showNotLoggedInState();
            }
            
            // 显示未登录状态
            function showNotLoggedInState() {
                document.querySelector('.profile-section').innerHTML = `
                    <div class="container">
                        <div class="empty-state">
                            <div class="empty-icon">🔒</div>
                            <h3>请先登录</h3>
                            <p>登录后即可查看和管理您的个人信息</p>
                            <a href="/login" class="btn btn-primary">立即登录</a>
                        </div>
                    </div>
                `;
            }
            
            // 填充用户数据到页面
            function populateUserData(userData, ordersData) {
                // 更新基本信息表单
                const form = document.querySelector('.profile-form');
                if (form) {
                    form.querySelector('input[name="username"]').value = userData.username || '';
                    form.querySelector('input[name="nickname"]').value = userData.nickname || '';
                    form.querySelector('input[name="email"]').value = userData.email || '';
                    
                    // 如果有手机号，显示脱敏版本
                    const phoneInput = form.querySelector('input[name="phone"]');
                    if (userData.phone) {
                        phoneInput.value = userData.phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
                    }
                    
                    // Steam信息
                    const steamIdInput = form.querySelector('input[name="steam_id"]');
                    if (steamIdInput && userData.steam_id) {
                        steamIdInput.value = userData.steam_id;
                    }
                }
                
                // 更新仪表板统计数据
                updateDashboardStats(userData, ordersData);
                
                // 更新近期活动
                updateRecentActivity(ordersData);
                
                // 更新用户头像和昵称
                updateUserProfile(userData);
            }
            
            // 更新仪表板统计
            function updateDashboardStats(userData, ordersData) {
                const statCards = document.querySelectorAll('.stat-card');
                
                // 确保ordersData是数组
                if (!Array.isArray(ordersData)) {
                    console.warn('ordersData不是数组，使用空数组');
                    ordersData = [];
                }
                
                // 计算统计数据
                const totalSpent = ordersData.length > 0 ? 
                    ordersData.reduce((sum, order) => sum + (parseFloat(order.total_amount) || 0), 0) : 0;
                const totalOrders = ordersData.length;
                const successfulOrders = ordersData.filter(order => 
                    order.status === 'completed' || order.status === 'delivered'
                ).length;
                
                // 更新统计卡片
                if (statCards[0]) {
                    statCards[0].querySelector('.stat-number').textContent = `¥${totalSpent.toFixed(2)}`;
                }
                if (statCards[1]) {
                    statCards[1].querySelector('.stat-number').textContent = totalOrders.toString();
                }
                if (statCards[2]) {
                    statCards[2].querySelector('.stat-number').textContent = successfulOrders.toString();
                }
                if (statCards[3]) {
                    // 显示用户余额
                    const balance = parseFloat(userData.balance) || 0;
                    statCards[3].querySelector('.stat-number').textContent = `¥${balance.toFixed(2)}`;
                }
            }
            
            // 更新近期活动
            function updateRecentActivity(ordersData) {
                const activityList = document.querySelector('.activity-list');
                if (!activityList) return;
                
                // 确保ordersData是数组
                if (!Array.isArray(ordersData)) {
                    console.warn('ordersData不是数组，使用空数组');
                    ordersData = [];
                }
                
                if (ordersData.length === 0) {
                    activityList.innerHTML = `
                        <div class="activity-item">
                            <div class="activity-icon">📝</div>
                            <div class="activity-content">
                                <div class="activity-title">暂无订单记录</div>
                                <div class="activity-time">快去购买您喜欢的游戏吧！</div>
                            </div>
                            <div class="activity-status">空</div>
                        </div>
                    `;
                    return;
                }
                
                // 获取最近3个订单
                const recentOrders = ordersData
                    .sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
                    .slice(0, 3);
                
                activityList.innerHTML = recentOrders.map(order => {
                    const statusMap = {
                        'pending': { text: '待支付', class: 'pending' },
                        'paid': { text: '已支付', class: 'success' },
                        'processing': { text: '处理中', class: 'pending' },
                        'completed': { text: '已完成', class: 'success' },
                        'delivered': { text: '已交付', class: 'success' },
                        'cancelled': { text: '已取消', class: 'error' },
                        'failed': { text: '失败', class: 'error' }
                    };
                    
                    const status = statusMap[order.status] || { text: '未知', class: '' };
                    const date = new Date(order.created_at).toLocaleString('zh-CN');
                    const amount = parseFloat(order.total_amount) || 0;
                    
                    return `
                        <div class="activity-item">
                            <div class="activity-icon">📦</div>
                            <div class="activity-content">
                                <div class="activity-title">${order.product_name || '订单'} - ¥${amount.toFixed(2)}</div>
                                <div class="activity-time">${date}</div>
                            </div>
                            <div class="activity-status ${status.class}">${status.text}</div>
                        </div>
                    `;
                }).join('');
            }
            
            // 更新用户资料显示
            function updateUserProfile(userData) {
                // 如果页面有用户头像显示区域，可以在这里更新
                // 暂时只在控制台输出，后续可以添加头像显示
                console.log('用户资料更新:', {
                    username: userData.username,
                    email: userData.email,
                    balance: userData.balance,
                    steam_id: userData.steam_id
                });
            }
            
            // 表单提交处理
            const profileForm = document.querySelector('.profile-form');
            const passwordForm = document.querySelector('.password-form');
            
            if (profileForm) {
                profileForm.addEventListener('submit', async function(e) {
                    e.preventDefault();
                    
                    const token = localStorage.getItem('access_token');
                    if (!token) {
                        showMessage('请先登录', 'error');
                        return;
                    }
                    
                    try {
                        // 获取表单数据
                        const formData = new FormData(this);
                        const updateData = {
                            email: formData.get('email') || this.querySelector('input[name="email"]').value,
                            phone: formData.get('phone') || this.querySelector('input[name="phone"]').value,
                            steam_id: formData.get('steam_id') || this.querySelector('input[name="steam_id"]').value
                        };
                        
                        // 发送更新请求
                        const response = await fetch('/api/v1/auth/profile', {
                            method: 'PUT',
                            headers: {
                                'Authorization': `Bearer ${token}`,
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify(updateData)
                        });
                        
                        if (response.ok) {
                            const updatedUser = await response.json();
                            
                            // 更新localStorage中的用户信息
                            const currentUserInfo = JSON.parse(localStorage.getItem('user_info') || '{}');
                            const newUserInfo = {
                                ...currentUserInfo,
                                email: updatedUser.email || updateData.email,
                                phone: updatedUser.phone || updateData.phone,
                                steam_id: updatedUser.steam_id || updateData.steam_id
                            };
                            localStorage.setItem('user_info', JSON.stringify(newUserInfo));
                            
                            // 更新导航栏显示
                            if (typeof navigation !== 'undefined' && navigation.updateUser) {
                                navigation.updateUser();
                            }
                            
                            showMessage('个人信息更新成功！', 'success');
                        } else {
                            const error = await response.json();
                            showMessage(error.detail || '更新失败', 'error');
                        }
                    } catch (error) {
                        console.error('更新个人信息失败:', error);
                        showMessage('网络错误，更新失败', 'error');
                    }
                });
            }
            
            if (passwordForm) {
                passwordForm.addEventListener('submit', async function(e) {
                    e.preventDefault();
                    
                    const token = localStorage.getItem('access_token');
                    if (!token) {
                        showMessage('请先登录', 'error');
                        return;
                    }
                    
                    const currentPassword = this.querySelector('input[placeholder="请输入当前密码"]').value;
                    const newPassword = this.querySelector('input[placeholder="请输入新密码"]').value;
                    const confirmPassword = this.querySelector('input[placeholder="再次输入新密码"]').value;
                    
                    if (newPassword !== confirmPassword) {
                        showMessage('两次输入的密码不一致', 'error');
                        return;
                    }
                    
                    try {
                        const response = await fetch('/api/v1/auth/change-password', {
                            method: 'POST',
                            headers: {
                                'Authorization': `Bearer ${token}`,
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                current_password: currentPassword,
                                new_password: newPassword
                            })
                        });
                        
                        if (response.ok) {
                            showMessage('密码修改成功！', 'success');
                            this.reset();
                        } else {
                            const error = await response.json();
                            showMessage(error.detail || '密码修改失败', 'error');
                        }
                    } catch (error) {
                        console.error('修改密码失败:', error);
                        showMessage('网络错误，修改失败', 'error');
                    }
                });
            }
            
            // 安全设置切换
            document.querySelectorAll('.security-toggle').forEach(btn => {
                btn.addEventListener('click', function() {
                    if (this.textContent === '启用') {
                        this.textContent = '已启用';
                        this.className = 'btn btn-primary security-toggle';
                        showMessage('安全验证已启用', 'success');
                    } else {
                        this.textContent = '启用';
                        this.className = 'btn btn-outline security-toggle';
                        showMessage('安全验证已关闭', 'info');
                    }
                });
            });
            
            // 积分兑换
            document.querySelectorAll('.reward-item .btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const rewardTitle = this.closest('.reward-item').querySelector('.reward-title').textContent;
                    const rewardCost = this.closest('.reward-item').querySelector('.reward-cost').textContent;
                    
                    if (confirm(`确定要兑换${rewardTitle}吗？\n需要消耗${rewardCost}`)) {
                        showMessage(`${rewardTitle}兑换成功！`, 'success');
                        // 更新积分余额
                        const currentPoints = parseInt(document.querySelector('.balance-amount').textContent);
                        const costPoints = parseInt(rewardCost);
                        document.querySelector('.balance-amount').textContent = currentPoints - costPoints;
                    }
                });
            });
        });
    </script>
</body>
</html> 