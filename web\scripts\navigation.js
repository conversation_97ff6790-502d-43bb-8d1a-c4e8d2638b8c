/**
 * 公共导航栏组件
 * 用于在所有页面中动态生成导航栏
 */

class Navigation {
    constructor() {
        this.currentPath = window.location.pathname;
        this.user = this.getCurrentUser();
    }

    /**
     * 获取当前用户信息
     */
    getCurrentUser() {
        const token = localStorage.getItem('access_token');
        const userInfo = localStorage.getItem('user_info');
        if (token && userInfo) {
            return JSON.parse(userInfo);
        }
        return null;
    }

    /**
     * 生成导航栏HTML
     */
    generateNavbar() {
        return `
            <nav class="navbar">
                <div class="container">
                    <div class="nav-brand">
                        <h1>SteamPY</h1>
                        <span class="nav-subtitle">Steam游戏交易平台</span>
                    </div>
                    
                    <div class="nav-menu">
                        <a href="/" class="nav-link ${this.isActive('/') ? 'active' : ''}">首页</a>
                        <a href="/products" class="nav-link ${this.isActive('/products') ? 'active' : ''}">产品商城</a>
                        <a href="/wallet" class="nav-link ${this.isActive('/wallet') ? 'active' : ''}">Steam钱包</a>
                        <a href="/orders" class="nav-link ${this.isActive('/orders') ? 'active' : ''}">我的订单</a>
                        <a href="/support" class="nav-link ${this.isActive('/support') ? 'active' : ''}">客服支持</a>
                    </div>
                    
                    <div class="nav-actions">
                        ${this.generateUserActions()}
                    </div>
                    
                    <!-- 移动端菜单按钮 -->
                    <button class="mobile-menu-toggle" onclick="navigation.toggleMobileMenu()">
                        <span></span>
                        <span></span>
                        <span></span>
                    </button>
                </div>
                
                <!-- 移动端菜单 -->
                <div class="mobile-menu" id="mobile-menu">
                    <div class="mobile-menu-content">
                        <a href="/" class="mobile-nav-link ${this.isActive('/') ? 'active' : ''}">首页</a>
                        <a href="/products" class="mobile-nav-link ${this.isActive('/products') ? 'active' : ''}">产品商城</a>
                        <a href="/wallet" class="mobile-nav-link ${this.isActive('/wallet') ? 'active' : ''}">Steam钱包</a>
                        <a href="/orders" class="mobile-nav-link ${this.isActive('/orders') ? 'active' : ''}">我的订单</a>
                        <a href="/support" class="mobile-nav-link ${this.isActive('/support') ? 'active' : ''}">客服支持</a>
                        <div class="mobile-nav-divider"></div>
                        ${this.generateMobileUserActions()}
                    </div>
                </div>
            </nav>
        `;
    }

    /**
     * 检查是否为当前页面
     */
    isActive(path) {
        if (path === '/' && this.currentPath === '/') {
            return true;
        }
        if (path !== '/' && this.currentPath.startsWith(path)) {
            return true;
        }
        return false;
    }

    /**
     * 生成用户操作区域
     */
    generateUserActions() {
        if (this.user) {
            return `
                <div class="user-info">
                    <span class="user-balance">余额: ¥${this.user.balance || 0}</span>
                    <div class="user-dropdown">
                        <button class="user-avatar" onclick="navigation.toggleUserMenu()">
                            <img src="${this.user.avatar_url || '/static/default-avatar.png'}" alt="用户头像">
                            <span>${this.user.username}</span>
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        <div class="user-menu" id="user-menu">
                            <a href="/profile" class="user-menu-item">
                                <i class="fas fa-user"></i>
                                个人中心
                            </a>
                            <a href="/orders" class="user-menu-item">
                                <i class="fas fa-list"></i>
                                我的订单
                            </a>
                            <div class="user-menu-divider"></div>
                            <button onclick="navigation.logout()" class="user-menu-item logout-btn">
                                <i class="fas fa-sign-out-alt"></i>
                                退出登录
                            </button>
                        </div>
                    </div>
                </div>
            `;
        } else {
            return `
                <a href="/login" class="btn btn-primary">登录</a>
                <a href="/register" class="btn btn-outline">注册</a>
            `;
        }
    }

    /**
     * 生成移动端用户操作区域
     */
    generateMobileUserActions() {
        if (this.user) {
            return `
                <div class="mobile-user-info">
                    <div class="mobile-user-header">
                        <img src="${this.user.avatar_url || '/static/default-avatar.png'}" alt="用户头像" class="mobile-avatar">
                        <div class="mobile-user-details">
                            <span class="mobile-username">${this.user.username}</span>
                            <span class="mobile-balance">余额: ¥${this.user.balance || 0}</span>
                        </div>
                    </div>
                    <a href="/profile" class="mobile-nav-link">个人中心</a>
                    <button onclick="navigation.logout()" class="mobile-nav-link logout-btn">退出登录</button>
                </div>
            `;
        } else {
            return `
                <a href="/login" class="mobile-nav-link">登录</a>
                <a href="/register" class="mobile-nav-link">注册</a>
            `;
        }
    }

    /**
     * 渲染导航栏到页面
     */
    render() {
        const navContainer = document.getElementById('navbar-container');
        if (navContainer) {
            navContainer.innerHTML = this.generateNavbar();
            this.attachEventListeners();
        } else {
            console.error('未找到导航栏容器元素 #navbar-container');
        }
    }

    /**
     * 绑定事件监听器
     */
    attachEventListeners() {
        // 点击页面其他地方关闭菜单
        document.addEventListener('click', (e) => {
            const userMenu = document.getElementById('user-menu');
            const mobileMenu = document.getElementById('mobile-menu');
            
            if (userMenu && !e.target.closest('.user-dropdown')) {
                userMenu.classList.remove('show');
            }
            
            if (mobileMenu && !e.target.closest('.mobile-menu-toggle') && !e.target.closest('.mobile-menu')) {
                mobileMenu.classList.remove('show');
            }
        });
    }

    /**
     * 切换用户菜单
     */
    toggleUserMenu() {
        const userMenu = document.getElementById('user-menu');
        if (userMenu) {
            userMenu.classList.toggle('show');
        }
    }

    /**
     * 切换移动端菜单
     */
    toggleMobileMenu() {
        const mobileMenu = document.getElementById('mobile-menu');
        if (mobileMenu) {
            mobileMenu.classList.toggle('show');
        }
    }

    /**
     * 用户退出登录
     */
    async logout() {
        try {
            const token = localStorage.getItem('access_token');
            if (token) {
                // 调用后端登出接口
                await fetch('/api/v1/auth/logout', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
            }
        } catch (error) {
            console.log('登出请求失败:', error);
        } finally {
            // 清除本地存储
            localStorage.removeItem('access_token');
            localStorage.removeItem('refresh_token');
            localStorage.removeItem('user_info');
            
            // 重定向到首页
            window.location.href = '/';
        }
    }

    /**
     * 更新用户信息
     */
    updateUser() {
        this.user = this.getCurrentUser();
        this.render();
    }
}

// 创建全局导航栏实例
const navigation = new Navigation();

// 页面加载完成后渲染导航栏
document.addEventListener('DOMContentLoaded', () => {
    navigation.render();
});

// 导出给其他模块使用
window.Navigation = Navigation;
window.navigation = navigation; 