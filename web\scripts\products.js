// 产品页面JavaScript功能
document.addEventListener('DOMContentLoaded', function() {
    
    // 产品数据（模拟）
    const products = [
        {
            id: 1,
            name: "Steam钱包充值码 ¥20",
            category: "wallet",
            price: 20,
            originalPrice: null,
            image: "https://images.unsplash.com/photo-1511512578047-dfb367046420?w=300&h=200&fit=crop",
            tags: ["热门", "即时到账"],
            features: ["✓ 正版保证", "⚡ 极速到账"]
        },
        {
            id: 2,
            name: "Steam钱包充值码 ¥50",
            category: "wallet",
            price: 50,
            originalPrice: null,
            image: "https://images.unsplash.com/photo-1511512578047-dfb367046420?w=300&h=200&fit=crop",
            tags: ["热门", "即时到账"],
            features: ["✓ 正版保证", "⚡ 极速到账"]
        },
        {
            id: 3,
            name: "Steam钱包充值码 ¥100",
            category: "wallet",
            price: 95,
            originalPrice: 100,
            image: "https://images.unsplash.com/photo-1511512578047-dfb367046420?w=300&h=200&fit=crop",
            tags: ["热门", "5%优惠"],
            features: ["✓ 正版保证", "⚡ 极速到账"]
        }
    ];
    
    // 搜索功能
    const searchInput = document.getElementById('search-input');
    const categoryFilter = document.getElementById('category-filter');
    const priceFilter = document.getElementById('price-filter');
    const sortFilter = document.getElementById('sort-filter');
    
    function filterProducts() {
        const searchTerm = searchInput.value.toLowerCase();
        const selectedCategory = categoryFilter.value;
        const selectedPriceRange = priceFilter.value;
        const selectedSort = sortFilter.value;
        
        const productCards = document.querySelectorAll('.product-card');
        const visibleProducts = [];
        
        productCards.forEach(card => {
            const productName = card.querySelector('.product-name').textContent.toLowerCase();
            const productCategory = card.dataset.category;
            const productPrice = parseFloat(card.dataset.price);
            
            let isVisible = true;
            
            // 搜索过滤
            if (searchTerm && !productName.includes(searchTerm)) {
                isVisible = false;
            }
            
            // 分类过滤
            if (selectedCategory && productCategory !== selectedCategory) {
                isVisible = false;
            }
            
            // 价格过滤
            if (selectedPriceRange) {
                const [min, max] = selectedPriceRange.split('-').map(p => p.replace('+', ''));
                const minPrice = parseFloat(min) || 0;
                const maxPrice = max ? parseFloat(max) : Infinity;
                
                if (productPrice < minPrice || productPrice > maxPrice) {
                    isVisible = false;
                }
            }
            
            if (isVisible) {
                card.style.display = 'block';
                visibleProducts.push({ element: card, price: productPrice });
            } else {
                card.style.display = 'none';
            }
        });
        
        // 排序
        if (selectedSort && visibleProducts.length > 0) {
            const container = document.getElementById('products-grid');
            
            switch (selectedSort) {
                case 'price-low':
                    visibleProducts.sort((a, b) => a.price - b.price);
                    break;
                case 'price-high':
                    visibleProducts.sort((a, b) => b.price - a.price);
                    break;
                // 可以添加更多排序选项
            }
            
            // 重新排列DOM元素
            visibleProducts.forEach(product => {
                container.appendChild(product.element);
            });
        }
    }
    
    // 绑定过滤事件
    searchInput.addEventListener('input', filterProducts);
    categoryFilter.addEventListener('change', filterProducts);
    priceFilter.addEventListener('change', filterProducts);
    sortFilter.addEventListener('change', filterProducts);
    
    // 购买按钮点击事件
    document.querySelectorAll('.product-buy-btn').forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.stopPropagation();
            const productCard = this.closest('.product-card');
            const productName = productCard.querySelector('.product-name').textContent;
            const productPrice = productCard.querySelector('.current-price').textContent;
            
            // 检查登录状态
            if (!authManager || !authManager.isLoggedIn) {
                showMessage('请先登录后再购买', 'error');
                setTimeout(() => {
                    window.location.href = 'login.html';
                }, 1000);
                return;
            }
            
            // 添加到购物车或直接购买
            const confirmed = confirm(`确认购买 ${productName}？\n价格：${productPrice}`);
            if (confirmed) {
                // 模拟购买流程
                showMessage('正在处理您的订单...', 'info');
                
                setTimeout(() => {
                    showMessage('购买成功！请查看我的订单', 'success');
                    // 可以跳转到订单页面
                }, 2000);
            }
        });
    });
    
    // 加载更多功能
    const loadMoreBtn = document.getElementById('load-more-btn');
    let currentPage = 1;
    
    loadMoreBtn.addEventListener('click', function() {
        this.textContent = '加载中...';
        this.disabled = true;
        
        // 模拟加载延迟
        setTimeout(() => {
            // 这里可以从API加载更多产品
            showMessage('已加载所有商品', 'info');
            this.style.display = 'none';
        }, 1000);
    });
    
    // URL参数处理（支持从其他页面链接过来的分类过滤）
    const urlParams = new URLSearchParams(window.location.search);
    const categoryParam = urlParams.get('category');
    if (categoryParam) {
        categoryFilter.value = categoryParam;
        filterProducts();
    }
    
    // 产品卡片悬停效果增强
    document.querySelectorAll('.product-card').forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-8px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
    
}); 