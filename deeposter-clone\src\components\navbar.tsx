import Link from "next/link";
import Image from "next/image";
import { Button } from "@/components/ui/button";

export function Navbar() {
  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-background/80 backdrop-blur-sm border-b border-border">
      <div className="container mx-auto px-4 py-4 flex items-center justify-between">
        <Link href="/" className="flex items-center gap-2">
          <Image
            src="/logo.png"
            alt="DeePoster Logo"
            width={36}
            height={36}
            className="w-9 h-9"
          />
          <span className="text-xl font-semibold">DeePoster</span>
        </Link>
        <nav className="hidden md:flex items-center gap-6">
          <Link href="/gallery" className="text-foreground/80 hover:text-foreground transition-colors">
            Gallery
          </Link>
          <Link href="/pricing" className="text-foreground/80 hover:text-foreground transition-colors">
            Pricing
          </Link>
        </nav>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" className="hidden md:inline-flex">
            Sign in
          </Button>
          <Button variant="default" size="sm">
            Go to create
          </Button>
        </div>
      </div>
    </header>
  );
}
