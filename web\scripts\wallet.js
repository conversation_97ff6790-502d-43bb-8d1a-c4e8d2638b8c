// 钱包充值页面JavaScript功能
document.addEventListener('DOMContentLoaded', function() {
    
    // 充值金额配置
    const amountConfig = {
        20: { amount: 20, bonus: 1 },
        50: { amount: 50, bonus: 3 },
        100: { amount: 100, bonus: 8 },
        200: { amount: 200, bonus: 20 },
        500: { amount: 500, bonus: 60 }
    };
    
    let selectedAmount = 0;
    let selectedPayment = '';
    let appliedCoupon = null;
    
    // 金额选择
    const amountCards = document.querySelectorAll('.amount-card');
    const customAmountDiv = document.getElementById('custom-amount');
    const customInput = document.getElementById('custom-input');
    const rechargeBtn = document.getElementById('recharge-btn');
    
    amountCards.forEach(card => {
        card.addEventListener('click', function() {
            // 移除所有选中状态
            amountCards.forEach(c => c.classList.remove('selected'));
            
            // 添加选中状态
            this.classList.add('selected');
            
            const amount = this.dataset.amount;
            
            if (amount === 'custom') {
                customAmountDiv.style.display = 'block';
                selectedAmount = 0;
            } else {
                customAmountDiv.style.display = 'none';
                selectedAmount = parseInt(amount);
            }
            
            updateOrderSummary();
        });
    });
    
    // 自定义金额输入
    customInput.addEventListener('input', function() {
        const value = parseInt(this.value) || 0;
        if (value >= 10 && value <= 5000) {
            selectedAmount = value;
            updateOrderSummary();
        } else {
            selectedAmount = 0;
            updateOrderSummary();
        }
    });
    
    // 支付方式选择
    const paymentOptions = document.querySelectorAll('.payment-option');
    
    paymentOptions.forEach(option => {
        option.addEventListener('click', function() {
            paymentOptions.forEach(o => o.classList.remove('selected'));
            this.classList.add('selected');
            selectedPayment = this.dataset.method;
            updateRechargeButton();
        });
    });
    
    // 更新订单摘要
    function updateOrderSummary() {
        const selectedAmountEl = document.getElementById('selected-amount');
        const bonusAmountEl = document.getElementById('bonus-amount');
        const totalAmountEl = document.getElementById('total-amount');
        const finalAmountEl = document.getElementById('final-amount');
        
        if (selectedAmount === 0) {
            selectedAmountEl.textContent = '请选择金额';
            bonusAmountEl.textContent = '¥0';
            totalAmountEl.textContent = '¥0';
            finalAmountEl.textContent = '¥0';
            return;
        }
        
        selectedAmountEl.textContent = `¥${selectedAmount}`;
        
        // 计算赠送金额
        let bonus = 0;
        if (amountConfig[selectedAmount]) {
            bonus = amountConfig[selectedAmount].bonus;
        } else {
            // 自定义金额的赠送比例
            if (selectedAmount >= 500) bonus = Math.floor(selectedAmount * 0.12);
            else if (selectedAmount >= 200) bonus = Math.floor(selectedAmount * 0.10);
            else if (selectedAmount >= 100) bonus = Math.floor(selectedAmount * 0.08);
            else if (selectedAmount >= 50) bonus = Math.floor(selectedAmount * 0.06);
            else if (selectedAmount >= 20) bonus = Math.floor(selectedAmount * 0.05);
        }
        
        bonusAmountEl.textContent = `¥${bonus}`;
        
        // 计算总费用（考虑优惠券）
        let total = selectedAmount;
        if (appliedCoupon) {
            total -= appliedCoupon.discount;
        }
        total = Math.max(total, 0);
        
        totalAmountEl.textContent = `¥${total}`;
        finalAmountEl.textContent = `¥${selectedAmount + bonus}`;
        
        updateRechargeButton();
    }
    
    // 更新充值按钮状态
    function updateRechargeButton() {
        const steamId = document.getElementById('steam-id').value.trim();
        
        if (selectedAmount > 0 && selectedPayment && steamId) {
            rechargeBtn.disabled = false;
            rechargeBtn.textContent = `立即充值 ¥${selectedAmount}`;
        } else {
            rechargeBtn.disabled = true;
            rechargeBtn.textContent = '请完善充值信息';
        }
    }
    
    // Steam ID输入检测
    document.getElementById('steam-id').addEventListener('input', updateRechargeButton);
    
    // 优惠券功能
    const couponInput = document.getElementById('coupon-code');
    const couponApplyBtn = document.querySelector('.coupon-apply-btn');
    const availableCoupons = document.querySelectorAll('.coupon-item');
    
    couponApplyBtn.addEventListener('click', function() {
        const code = couponInput.value.trim().toLowerCase();
        
        // 模拟优惠券验证
        const coupons = {
            'new10': { discount: 10, name: '新用户10元券' },
            'vip5': { discount: 5, name: 'VIP专享5元券' },
            'welcome': { discount: 3, name: '欢迎券' }
        };
        
        if (coupons[code]) {
            appliedCoupon = coupons[code];
            showMessage(`成功使用优惠券：${appliedCoupon.name}`, 'success');
            updateOrderSummary();
            couponInput.value = '';
        } else {
            showMessage('优惠券无效或已过期', 'error');
        }
    });
    
    // 可用优惠券点击使用
    availableCoupons.forEach(coupon => {
        coupon.addEventListener('click', function() {
            const discount = parseInt(this.dataset.discount);
            const name = this.querySelector('.coupon-name').textContent;
            
            appliedCoupon = { discount, name };
            showMessage(`成功使用优惠券：${name}`, 'success');
            updateOrderSummary();
            this.style.opacity = '0.5';
            this.style.pointerEvents = 'none';
        });
    });
    
    // 充值按钮点击
    rechargeBtn.addEventListener('click', function() {
        // 检查登录状态
        if (!authManager || !authManager.isLoggedIn) {
            showMessage('请先登录后再进行充值', 'error');
            setTimeout(() => {
                window.location.href = 'login.html';
            }, 1000);
            return;
        }
        
        const steamId = document.getElementById('steam-id').value.trim();
        
        // 确认充值信息
        const finalAmount = selectedAmount + (amountConfig[selectedAmount]?.bonus || 0);
        const confirmMessage = `
确认充值信息：
Steam账户：${steamId}
充值金额：¥${selectedAmount}
实际到账：¥${finalAmount}
支付方式：${getPaymentName(selectedPayment)}

确认充值吗？
        `;
        
        if (confirm(confirmMessage)) {
            processRecharge();
        }
    });
    
    // 处理充值
    function processRecharge() {
        rechargeBtn.disabled = true;
        rechargeBtn.textContent = '正在处理...';
        
        // 模拟充值处理
        showMessage('正在处理您的充值订单...', 'info');
        
        setTimeout(() => {
            // 模拟成功
            showMessage('充值成功！Steam余额已到账', 'success');
            
            // 跳转到订单页面
            setTimeout(() => {
                window.location.href = 'orders.html';
            }, 2000);
        }, 3000);
    }
    
    // 获取支付方式名称
    function getPaymentName(method) {
        const names = {
            'alipay': '支付宝',
            'wechat': '微信支付',
            'bank': '银行卡'
        };
        return names[method] || '未知';
    }
    
    // FAQ展开/收起
    const faqItems = document.querySelectorAll('.faq-item');
    
    faqItems.forEach(item => {
        const question = item.querySelector('.faq-question');
        const answer = item.querySelector('.faq-answer');
        const toggle = item.querySelector('.faq-toggle');
        
        question.addEventListener('click', function() {
            const isOpen = answer.style.display === 'block';
            
            // 关闭所有FAQ
            faqItems.forEach(faq => {
                faq.querySelector('.faq-answer').style.display = 'none';
                faq.querySelector('.faq-toggle').textContent = '+';
            });
            
            // 切换当前FAQ
            if (!isOpen) {
                answer.style.display = 'block';
                toggle.textContent = '-';
            }
        });
    });
    
    // 页面加载时的初始化
    updateOrderSummary();
    
}); 