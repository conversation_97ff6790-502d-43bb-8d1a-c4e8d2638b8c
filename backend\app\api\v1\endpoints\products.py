from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.models.product import Product, ProductType, ProductStatus
from app.models.user import User
from app.api.v1.endpoints.auth import get_current_user
from typing import List, Optional
from pydantic import BaseModel
from decimal import Decimal

router = APIRouter()


class ProductResponse(BaseModel):
    id: int
    name: str
    name_en: str | None = None
    description: str | None = None
    short_description: str | None = None
    product_type: str
    status: str
    steam_app_id: str | None = None
    steam_app_name: str | None = None
    original_price: float | None = None
    selling_price: float
    discount_percentage: int
    stock_quantity: int
    sold_quantity: int
    cover_image_url: str | None = None
    thumbnail_url: str | None = None
    category: str | None = None
    rating: float
    review_count: int
    is_featured: bool
    is_hot: bool
    
    class Config:
        from_attributes = True


class ProductCreate(BaseModel):
    name: str
    name_en: str | None = None
    description: str | None = None
    short_description: str | None = None
    product_type: ProductType
    steam_app_id: str | None = None
    steam_app_name: str | None = None
    original_price: float | None = None
    selling_price: float
    discount_percentage: int = 0
    stock_quantity: int = 0
    cover_image_url: str | None = None
    thumbnail_url: str | None = None
    category: str | None = None


@router.get("/", response_model=List[ProductResponse])
async def get_products(
    skip: int = 0,
    limit: int = 20,
    product_type: Optional[ProductType] = None,
    category: Optional[str] = None,
    status: Optional[ProductStatus] = None,
    is_featured: Optional[bool] = None,
    is_hot: Optional[bool] = None,
    db: Session = Depends(get_db)
):
    """获取产品列表"""
    query = db.query(Product)
    
    if product_type:
        query = query.filter(Product.product_type == product_type)
    if category:
        query = query.filter(Product.category == category)
    if status:
        query = query.filter(Product.status == status)
    if is_featured is not None:
        query = query.filter(Product.is_featured == is_featured)
    if is_hot is not None:
        query = query.filter(Product.is_hot == is_hot)
    
    # 默认只显示活跃状态的产品
    if status is None:
        query = query.filter(Product.status == ProductStatus.ACTIVE)
    
    products = query.offset(skip).limit(limit).all()
    return [ProductResponse.from_orm(product) for product in products]


@router.get("/featured", response_model=List[ProductResponse])
async def get_featured_products(
    limit: int = 8,
    db: Session = Depends(get_db)
):
    """获取特色产品"""
    products = db.query(Product).filter(
        Product.is_featured == True,
        Product.status == ProductStatus.ACTIVE
    ).limit(limit).all()
    
    return [ProductResponse.from_orm(product) for product in products]


@router.get("/hot", response_model=List[ProductResponse])
async def get_hot_products(
    limit: int = 8,
    db: Session = Depends(get_db)
):
    """获取热门产品"""
    products = db.query(Product).filter(
        Product.is_hot == True,
        Product.status == ProductStatus.ACTIVE
    ).order_by(Product.sold_quantity.desc()).limit(limit).all()
    
    return [ProductResponse.from_orm(product) for product in products]


@router.get("/{product_id}", response_model=ProductResponse)
async def get_product(
    product_id: int,
    db: Session = Depends(get_db)
):
    """获取产品详情"""
    product = db.query(Product).filter(Product.id == product_id).first()
    if not product:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Product not found"
        )
    
    return ProductResponse.from_orm(product)


@router.post("/", response_model=ProductResponse)
async def create_product(
    product_data: ProductCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """创建产品 (管理员功能)"""
    if current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Permission denied"
        )
    
    product = Product(**product_data.dict())
    product.seller_id = current_user.id
    
    db.add(product)
    db.commit()
    db.refresh(product)
    
    return ProductResponse.from_orm(product) 