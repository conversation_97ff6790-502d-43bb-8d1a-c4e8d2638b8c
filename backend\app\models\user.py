from sqlalchemy import Column, String, Boolean, DateTime, Integer, Enum, Text, DECIMAL
from sqlalchemy.orm import relationship
from .base import BaseModel
import enum


class UserRole(str, enum.Enum):
    """用户角色枚举"""
    ADMIN = "admin"
    USER = "user"
    SELLER = "seller"


class UserStatus(str, enum.Enum):
    """用户状态枚举"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    BANNED = "banned"
    PENDING = "pending"


class User(BaseModel):
    """用户模型"""
    __tablename__ = "users"

    # 基本信息
    username = Column(String(50), unique=True, index=True, nullable=False)
    email = Column(String(100), unique=True, index=True, nullable=False)
    hashed_password = Column(String(128), nullable=False)
    
    # 用户详情
    nickname = Column(String(50), nullable=True)
    avatar_url = Column(String(255), nullable=True)
    phone = Column(String(20), nullable=True)
    
    # 状态和权限
    role = Column(Enum(UserRole), default=UserRole.USER, nullable=False)
    status = Column(Enum(UserStatus), default=UserStatus.PENDING, nullable=False)
    is_verified = Column(Boolean, default=False, nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    
    # Steam相关
    steam_id = Column(String(17), unique=True, nullable=True, index=True)
    steam_username = Column(String(50), nullable=True)
    steam_profile_url = Column(String(255), nullable=True)
    steam_avatar_url = Column(String(255), nullable=True)
    
    # 账户信息
    balance = Column(DECIMAL(10, 2), default=0.00, nullable=False)
    frozen_balance = Column(DECIMAL(10, 2), default=0.00, nullable=False)
    
    # 统计信息
    total_orders = Column(Integer, default=0, nullable=False)
    successful_orders = Column(Integer, default=0, nullable=False)
    
    # 最后活动时间
    last_login_at = Column(DateTime(timezone=True), nullable=True)
    last_activity_at = Column(DateTime(timezone=True), nullable=True)
    
    # 备注
    notes = Column(Text, nullable=True)
    
    def __repr__(self):
        return f"<User(id={self.id}, username='{self.username}', email='{self.email}')>"


class UserSession(BaseModel):
    """用户会话模型"""
    __tablename__ = "user_sessions"
    
    user_id = Column(Integer, nullable=False, index=True)
    session_token = Column(String(255), unique=True, nullable=False, index=True)
    refresh_token = Column(String(255), unique=True, nullable=False, index=True)
    expires_at = Column(DateTime(timezone=True), nullable=False)
    ip_address = Column(String(45), nullable=True)
    user_agent = Column(String(500), nullable=True)
    is_active = Column(Boolean, default=True, nullable=False) 