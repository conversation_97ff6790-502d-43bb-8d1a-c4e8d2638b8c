@echo off
echo 🚀 启动 SteamPY 项目 (Windows版本)...

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python 未安装，请先安装 Python 3.11+
    pause
    exit /b 1
)

REM 检查Node.js是否安装
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js 未安装，请先安装 Node.js 18+
    pause
    exit /b 1
)

REM 检查Node.js版本
for /f "tokens=1 delims=v" %%i in ('node --version 2^>nul') do set NODE_VERSION=%%i
if defined NODE_VERSION (
    echo 📋 检测到 Node.js 版本: %NODE_VERSION%
    echo ⚠️  注意: 如果前端启动失败，请升级到 Node.js 18+ 或使用降级版本
) else (
    echo ❌ 无法检测 Node.js 版本
)

REM 切换到后端目录
cd backend

REM 创建虚拟环境（如果不存在）
if not exist "venv" (
    echo 📦 创建Python虚拟环境...
    python -m venv venv
)

REM 激活虚拟环境
echo 🔧 激活虚拟环境...
call venv\Scripts\activate.bat

REM 安装依赖
echo 📦 安装后端依赖...
pip install -r requirements.txt

REM 启动后端服务
echo 🚀 启动后端服务...
start "SteamPY Backend" cmd /k "python main.py"

REM 等待一下让后端启动
timeout /t 5 /nobreak >nul

REM 切换到前端目录
cd ..\frontend

REM 安装前端依赖
echo 📦 安装前端依赖...
npm install

REM 启动前端服务
echo 🚀 启动前端开发服务...
start "SteamPY Frontend" cmd /k "npm run dev"

echo.
echo ✅ 项目启动完成！
echo 🌐 前端地址: http://localhost:3000
echo 🔗 后端API: http://localhost:8000
echo 📚 API文档: http://localhost:8000/docs
echo 🗄️  数据库: SQLite (backend\steampy.db)
echo.
echo 📋 默认管理员账户:
echo    用户名: admin
echo    密码: admin123
echo.
echo 💡 版本兼容性说明:
echo    - 当前项目已降级到 Next.js 13 以兼容 Node.js 16
echo    - 推荐升级到 Node.js 18+ 以获得最佳体验
echo    - 访问 https://nodejs.org/ 下载最新版本
echo.
echo ⏹️  关闭窗口即可停止对应服务
pause 