export function PosterInputPlaceholder() {
  return (
    <div className="rounded-lg bg-card border border-border p-6 w-full max-w-md mx-auto">
      <div className="text-center text-xl font-semibold mb-4">DeePoster</div>
      <div className="space-y-3 mb-6">
        <div className="flex space-x-2">
          <div className="bg-secondary rounded-md px-3 py-2 text-xs inline-block">Add URL</div>
          <div className="bg-secondary rounded-md px-3 py-2 text-xs inline-block">Add text</div>
          <div className="bg-secondary rounded-md px-3 py-2 text-xs inline-block">Upload file</div>
        </div>
        <div className="bg-muted h-10 rounded-md" />
        <div className="flex justify-between items-center">
          <div>
            <div className="text-xs text-muted-foreground mb-1">Aspect ratio</div>
            <div className="bg-secondary rounded-md px-3 py-1 text-xs inline-block">9:16</div>
          </div>
          <div>
            <div className="text-xs text-muted-foreground mb-1">Content Type</div>
            <div className="flex space-x-2">
              <div className="bg-primary rounded-md px-3 py-1 text-xs inline-block">Precise</div>
              <div className="bg-secondary rounded-md px-3 py-1 text-xs inline-block">Descriptive</div>
            </div>
          </div>
        </div>
        <div>
          <div className="text-xs text-muted-foreground mb-1">Style</div>
          <div className="flex space-x-2 flex-wrap">
            <div className="bg-primary rounded-md px-3 py-1 text-xs inline-block mb-2">Auto</div>
            <div className="bg-secondary rounded-md px-3 py-1 text-xs inline-block mb-2">General</div>
            <div className="bg-secondary rounded-md px-3 py-1 text-xs inline-block mb-2">Realistic</div>
            <div className="bg-secondary rounded-md px-3 py-1 text-xs inline-block mb-2">Design</div>
            <div className="bg-secondary rounded-md px-3 py-1 text-xs inline-block mb-2">3D</div>
            <div className="bg-secondary rounded-md px-3 py-1 text-xs inline-block mb-2">Anime</div>
          </div>
        </div>
      </div>
      <div className="text-right text-xs text-muted-foreground mb-3">Uses 2 credits</div>
      <button className="w-full bg-primary hover:bg-primary/90 text-center py-2 rounded-md font-medium">
        CREATE NOW
      </button>
    </div>
  );
}

export function PosterResultPlaceholder() {
  return (
    <div className="rounded-lg bg-card border border-border p-6 w-full max-w-md mx-auto">
      <div className="bg-gradient-to-br from-blue-800 to-purple-600 h-80 rounded-md flex items-center justify-center mb-4">
        <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 w-3/4 text-center">
          <div className="text-lg font-bold mb-2">OPENAI'S Q1 MODEL PROGRESS</div>
          <div className="text-sm mb-4">PRICE, PRICE, AND POTENTIAL</div>
          <div className="grid grid-cols-3 gap-4">
            <div className="bg-white/20 rounded p-2">
              <div className="text-xs font-bold mb-1">LATEST</div>
              <div className="text-xs">INNOVATIONS</div>
            </div>
            <div className="bg-white/20 rounded p-2">
              <div className="text-xs font-bold mb-1">MARKET</div>
              <div className="text-xs">DISRUPTION</div>
            </div>
            <div className="bg-white/20 rounded p-2">
              <div className="text-xs font-bold mb-1">ONGOING</div>
              <div className="text-xs">RESEARCH</div>
            </div>
          </div>
        </div>
      </div>
      <button className="w-full bg-primary hover:bg-primary/90 text-center py-2 rounded-md font-medium mb-3">
        DOWNLOAD
      </button>
      <div className="flex justify-between">
        <button className="bg-secondary hover:bg-secondary/90 text-center py-1 px-3 rounded-md text-sm">
          Regenerate
        </button>
        <button className="bg-secondary hover:bg-secondary/90 text-center py-1 px-3 rounded-md text-sm">
          Try Different Style
        </button>
      </div>
    </div>
  );
}

export function DownloadButtonPlaceholder() {
  return (
    <div className="border border-border rounded-lg p-4 w-full max-w-xs mx-auto text-center">
      <div className="bg-card p-6 rounded-md inline-block mb-4">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="mx-auto mb-2"
        >
          <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
          <polyline points="7 10 12 15 17 10" />
          <line x1="12" y1="15" x2="12" y2="3" />
        </svg>
        <div className="text-lg font-semibold">Download</div>
      </div>
    </div>
  );
}
