from fastapi import FastAP<PERSON>, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse, HTMLResponse, RedirectResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from app.core.config import settings
from app.core.middleware import rate_limit_middleware
import os

# 重要：在初始化数据库之前导入所有模型
from app.models.user import User, UserSession, UserRole, UserStatus
from app.models.product import Product, Order, CDKInventory, ProductType, ProductStatus, OrderStatus, OrderType

# 然后导入数据库初始化
from app.core.database import init_db
from app.api.v1.api import api_router
import uvicorn

# 创建FastAPI应用实例
app = FastAPI(
    title=settings.APP_NAME,
    version=settings.APP_VERSION,
    description="SteamPY - Steam游戏交易平台 API",
    docs_url="/docs" if settings.DEBUG else None,
    redoc_url="/redoc" if settings.DEBUG else None,
)

# 添加速率限制中间件
if settings.RATE_LIMIT_ENABLED:
    app.middleware("http")(rate_limit_middleware)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 添加可信主机中间件
app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=["localhost", "127.0.0.1", "*.steampy.com", "*"]  # 开发环境允许所有主机
)


# 安全头中间件
@app.middleware("http")
async def add_security_headers(request: Request, call_next):
    response = await call_next(request)
    
    # 添加安全头
    response.headers["X-Content-Type-Options"] = "nosniff"
    response.headers["X-Frame-Options"] = "DENY"
    response.headers["X-XSS-Protection"] = "1; mode=block"
    response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
    
    if not settings.DEBUG:
        response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
    
    return response


# 全局异常处理
@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "success": False,
            "message": exc.detail,
            "error_code": exc.status_code,
            "path": request.url.path
        }
    )


@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    if settings.DEBUG:
        import traceback
        print(f"❌ 未处理的异常: {exc}")
        print(f"📍 请求路径: {request.url.path}")
        print(f"🔍 请求方法: {request.method}")
        print(traceback.format_exc())
    
    return JSONResponse(
        status_code=500,
        content={
            "success": False,
            "message": "Internal server error" if not settings.DEBUG else str(exc),
            "error_code": 500,
            "path": request.url.path
        }
    )


# 验证异常处理
@app.exception_handler(ValueError)
async def validation_exception_handler(request: Request, exc: ValueError):
    return JSONResponse(
        status_code=422,
        content={
            "success": False,
            "message": str(exc),
            "error_code": 422,
            "path": request.url.path
        }
    )


# 配置静态文件和模板
WEB_DIR = os.path.join(os.path.dirname(__file__), "..", "web")
WEB_DIR = os.path.abspath(WEB_DIR)

# 挂载静态文件
app.mount("/static", StaticFiles(directory=os.path.join(WEB_DIR, "styles")), name="static")
app.mount("/scripts", StaticFiles(directory=os.path.join(WEB_DIR, "scripts")), name="scripts")

# 配置模板
templates = Jinja2Templates(directory=WEB_DIR)


# 健康检查端点
@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {
        "status": "healthy",
        "version": settings.APP_VERSION,
        "environment": "development" if settings.DEBUG else "production",
        "database": "connected"
    }


# 根路径 - 首页
@app.get("/", response_class=HTMLResponse)
async def read_index(request: Request):
    """首页"""
    with open(os.path.join(WEB_DIR, "index.html"), "r", encoding="utf-8") as f:
        content = f.read()
    return HTMLResponse(content=content)


# 产品页面
@app.get("/products", response_class=HTMLResponse)
async def read_products(request: Request):
    """产品页面"""
    with open(os.path.join(WEB_DIR, "products.html"), "r", encoding="utf-8") as f:
        content = f.read()
    return HTMLResponse(content=content)


# Steam钱包页面
@app.get("/wallet", response_class=HTMLResponse)
async def read_wallet(request: Request):
    """Steam钱包页面"""
    with open(os.path.join(WEB_DIR, "wallet.html"), "r", encoding="utf-8") as f:
        content = f.read()
    return HTMLResponse(content=content)


# 登录页面
@app.get("/login", response_class=HTMLResponse)
async def read_login(request: Request):
    """登录页面"""
    with open(os.path.join(WEB_DIR, "login.html"), "r", encoding="utf-8") as f:
        content = f.read()
    return HTMLResponse(content=content)


# 注册页面
@app.get("/register", response_class=HTMLResponse)
async def read_register(request: Request):
    """注册页面"""
    with open(os.path.join(WEB_DIR, "register.html"), "r", encoding="utf-8") as f:
        content = f.read()
    return HTMLResponse(content=content)


# 订单页面
@app.get("/orders", response_class=HTMLResponse)
async def read_orders(request: Request):
    """订单页面"""
    with open(os.path.join(WEB_DIR, "orders.html"), "r", encoding="utf-8") as f:
        content = f.read()
    return HTMLResponse(content=content)


# 个人中心页面
@app.get("/profile", response_class=HTMLResponse)
async def read_profile(request: Request):
    """个人中心页面"""
    with open(os.path.join(WEB_DIR, "profile.html"), "r", encoding="utf-8") as f:
        content = f.read()
    return HTMLResponse(content=content)


# 客服支持页面
@app.get("/support", response_class=HTMLResponse)
async def read_support(request: Request):
    """客服支持页面"""
    with open(os.path.join(WEB_DIR, "support.html"), "r", encoding="utf-8") as f:
        content = f.read()
    return HTMLResponse(content=content)


# 现代化演示页面
@app.get("/modern-demo", response_class=HTMLResponse)
async def read_modern_demo(request: Request):
    """现代化演示页面"""
    try:
        with open(os.path.join(WEB_DIR, "modern-demo.html"), "r", encoding="utf-8") as f:
            content = f.read()
        return HTMLResponse(content=content)
    except FileNotFoundError:
        return HTMLResponse(content="<h1>Modern Demo Page Not Found</h1>", status_code=404)


# 注册API路由
app.include_router(api_router, prefix="/api/v1")

# 打印配置信息
if os.path.exists(WEB_DIR):
    print(f"✅ 前端静态文件服务已配置: {WEB_DIR}")
else:
    print(f"⚠️ 前端目录不存在: {WEB_DIR}")


# 启动事件
@app.on_event("startup")
async def startup_event():
    print(f"🚀 {settings.APP_NAME} v{settings.APP_VERSION} is starting...")
    print(f"🗄️  数据库类型: SQLite (内部集成)")
    print(f"🔧 调试模式: {settings.DEBUG}")
    print(f"🛡️  速率限制: {'启用' if settings.RATE_LIMIT_ENABLED else '禁用'}")
    
    # 初始化数据库
    try:
        print("📋 已导入的模型:")
        from app.models.base import Base
        for name, cls in Base.registry._class_registry.items():
            if hasattr(cls, '__tablename__'):
                print(f"   - {cls.__tablename__} ({cls.__name__})")
        
        init_db()
        print(f"✅ 数据库初始化完成")
    except Exception as e:
        print(f"❌ 数据库初始化失败: {e}")
        import traceback
        traceback.print_exc()
    
    print(f"📖 API documentation available at: http://localhost:8000/docs")
    print(f"🏥 Health check available at: http://localhost:8000/health")


# 关闭事件
@app.on_event("shutdown")
async def shutdown_event():
    print("🛑 SteamPY API is shutting down...")


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="127.0.0.1",
        port=8000,
        reload=settings.DEBUG,
        log_level="info" if not settings.DEBUG else "debug"
    )
